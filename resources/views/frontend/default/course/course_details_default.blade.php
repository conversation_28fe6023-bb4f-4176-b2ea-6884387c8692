@push('title', $course_details->title)
@push('meta')@endpush
@push('css')
    {{--    <link rel="stylesheet" href="{{ asset('assets/frontend/course-shopee/assets/css/common.css') }}"/>--}}
    {{--    <link rel="stylesheet" href="{{ asset('assets/frontend/course-shopee/assets/css/styles.css') }}"/>--}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    {{--Các modal--}}
    <style>

        .modules-section .countdown-section {
            border-radius: 15px;
            background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
            width: fit-content;
            margin: 13px auto 0;
            padding: 10px 30px;
        }

        .modules-section .countdown-section > p {
            color: #333;
            margin-bottom: 2px;
            font-size: 18px;
            font-weight: 600;
        }

        .modules-section .countdown-timer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            color: #ef7318;
            text-align: center;
            font-variant-numeric: lining-nums proportional-nums;
            font-family: Inter, sans-serif;
            font-size: 32px;
            font-style: normal;
            font-weight: 800;
            line-height: 28px; /* 87.5% */
            letter-spacing: 1.92px;
            gap: 15px;
            margin-top: 10px;
        }

        .countdown-timer .timer-box {
            display: flex;
            flex-direction: column;
            border: 2px solid #ef7318;
            padding: 10px 6px;
            border-radius: 7px;
            gap: 4px;
            color: #ef7318;
            width: 66px;
        }

        .countdown-timer .timer-box .timer-box-label {
            font-size: 16px;
            text-transform: uppercase;
        }

        .elearning-course .course-header .nav-link.active svg * {
            fill: #eb2805 !important;
        }

        /* Styles for fixed price display */
        .fixed-price-container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
            border: 2px solid #ff6600;
        }

        .discount-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: #ff6600;
            color: #fff;
            padding: 8px 15px;
            font-weight: bold;
            font-size: 16px;
            border-bottom-left-radius: 12px;
            box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.1);
        }

        .price-display {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 15px 0;
        }

        .original-price, .sale-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .original-price .price-label {
            font-size: 18px;
            color: #666;
        }

        .original-price .price-value {
            font-size: 20px;
            color: #888;
            text-decoration: line-through;
            position: relative;
        }

        .sale-price .price-label {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .sale-price .price-value {
            font-size: 26px;
            font-weight: bold;
            color: #ff6600;
        }

        .savings {
            margin-top: 20px;
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 4px solid #ff6600;
        }

        .savings-text {
            font-size: 16px;
            color: #555;
        }

        .savings-value {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }

        .start-course-btn {
            background: linear-gradient(to right, #ff8a00, #ff5722);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 16px;
            text-transform: uppercase;
            box-shadow: 0 4px 10px rgba(255, 138, 0, 0.2);
            width: 240px;
        }

        @media (max-width: 768px) {
            .price-display {
                gap: 10px;
            }

            .original-price .price-label,
            .sale-price .price-label {
                font-size: 16px;
            }

            .original-price .price-value {
                font-size: 18px;
            }

            .sale-price .price-value {
                font-size: 22px;
            }

            .savings {
                padding: 10px;
            }

            .savings-text,
            .savings-value {
                font-size: 15px;
            }
        }

        #qrcode img {
            width: 100%;
        }

        #cancel_coupon,
        .login-link {
            cursor: pointer;
        }
    </style>
    <style>

        .modal-regis .modal-dialog {
            max-width: 690px;
        }

        .modal-auth #authTabs {
            border: none;
        }

        .modal-auth {
            max-width: 690px;
        }

        .modal-auth .modal-body {
            padding: 20px 50px 40px !important;
        }

        .modal-auth .form-login {
            border-radius: 28px;
            border: 2px solid var(--gradient-2, #ff5a07);
            background: #fff;
            box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .form-wrap {
            position: relative;
        }

        .modal-auth .form-wrap {
            position: relative;
        }

        .modal-auth .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-auth .modal-content {
            border-radius: 25px;
            overflow: hidden;
            border: none;
        }

        .modal-auth .modal-header {
            background: #105ce4 !important;
            color: #fbed0b;
            border-bottom: none;
            padding: 1.25rem 1.5rem 0.5rem;
            position: relative;
            text-align: center;
        }

        .modal-auth .modal-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
            margin-bottom: 5px;
        }

        .modal-auth .close-btn {
            position: absolute;
            right: 15px;
            top: 10px;
            background: transparent;
            border: none;
            color: white;
            font-size: 21px;
            padding: 0;
        }

        .modal-auth .modal-header p {
            color: white;
            font-size: 0.95rem;
            margin-top: 0.5rem;
        }

        .modal-auth .google-btn {
            margin-bottom: 1rem;
            padding: 0.75rem;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            color: #222;
            border-radius: 10px;
            border: 1px solid #ffe6e8;
            background: linear-gradient(180deg, #fff 0%, #ffeff0 100%);
        }

        .modal-auth .google-logo {
            margin-right: 0.5rem;
        }

        .modal-auth .tabs-container {
            display: flex;
            position: relative;
            overflow: hidden;
            border-radius: 25px 25px 0 0;
        }

        .modal-auth .tab-switch-auth span {
            color: #fff;
            -webkit-text-fill-color: #fff;
        }

        .modal-auth .tab-switch-auth .active span {
            background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            border: none !important;
        }

        .modal-auth .tab-switch-auth .tab.tab-active {
            background: #fff;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: #fff;
        }

        .modal-auth .tab-switch-auth .tab img {
            position: absolute;
            width: 100%;
            left: 0;
            z-index: 0;
            height: 100%;
            opacity: 1;
            transition: opacity 0.3s;
        }

        .modal-auth .tab-switch-auth .active img {
            opacity: 0;
        }

        .modal-auth .tab {
            flex: 1;
            text-align: center;
            height: 60px;
            font-weight: 700;
            color: white;
            cursor: pointer;
            position: relative;
            z-index: 1;
            font-size: 1.1rem;
        }

        .modal-auth .tab-active {
            color: #f33;
        }

        .modal-auth .tab-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 50%;
            height: 100%;
            background-color: white;
            z-index: 0;
            transition: transform 0.3s ease;
        }

        .modal-auth .tab-content {
            border: 1px solid #ffe0dc;
            border-top: none;
            border-radius: 0 0 25px 25px;
            padding: 20px 24px;
            background-color: white;
        }

        .form-label {
            margin-bottom: 0.25rem;
            font-size: 16px;
            position: absolute;
            background: #fff;
            top: -12px;
            z-index: 10;
            left: 14px;
            padding: 0 3px;
            color: #333;
        }

        .modal-auth .form-label {
            margin-bottom: 0.25rem;
            font-size: 16px;
            position: absolute;
            background: #fff;
            top: -12px;
            z-index: 10;
            left: 14px;
            padding: 0 3px;
            color: #333;
        }

        .modal-auth .form-control {
            border-radius: 10px;
            padding: 0.75rem;
            border-radius: 5px;
            border: 1px solid #dadada;
            height: 52px;
        }

        .modal-auth .form-text {
            color: #dc3f2e;
            font-weight: 500;
        }

        .modal-auth .red-text {
            color: #f33;
            font-weight: 500;
        }

        .password-field {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            background: none;
            border: none;
            cursor: pointer;
        }

        .modal-auth .signup-btn {
            background: linear-gradient(90deg, #ffb700 0%, #f33 100%);
            border-radius: 15px;
            margin-top: 1.5rem;
            width: 100%;
            border-radius: 10px;
            background: var(
                --gradient-2,
                linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
            );
            border: none;
            outline: none;
            font-size: 20px !important;
            font-weight: bold;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-auth .free-badge {
            position: relative;
            width: 60px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-auth .free-circle {
            width: 50px;
            height: 50px;
            background-color: #f33;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: rotate(-15deg);
        }

        .modal-auth .free-circle span {
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .modal-auth .btn-text {
            text-align: left;
            padding-left: 0.5rem;
            font-size: 19px !important;
        }

        .modal-auth .btn-title {
            font-size: 20px;
            line-height: 1.2;
        }

        .modal-auth .btn-subtitle {
            font-size: 14px;
            font-weight: 400;
            opacity: 0.9;
        }

        .modal-auth .nav-tabs .nav-link {
            width: 100%;
            border: none;
            background: none;
            padding: 0;
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .modal-regis .img-bank {
            position: absolute;
            width: calc(100% - 40px);
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: contain;
        }

        .modal-regis .border-bank {
            aspect-ratio: 1;
            width: 100%;
        }

        .modal-regis .body-method-option {
            background: #f7f7f7;
            padding: 0 10px;
        }

        .modal-regis #btn-checkout-regis {
            border-radius: 10px;
            background: var(
                --gradient-2,
                linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
            );
            border: none;
            display: block;
            width: 100%;
            height: 56px;
            font-size: 18px;
            text-transform: uppercase;
            font-weight: bold;
            color: #fff;
        }

        .modal-regis .phone-group {
            border: 1px solid #dadada;
            border-radius: 5px;
        }

        .modal-regis .phone-group input {
            border: none;
        }

        .modal-regis .phone-group button {
            border: none;
            border-right: 1px solid #dadada;
            border-radius: 0;
            display: flex;
            width: 110px;
            font-size: 16px;
            align-items: center;
            gap: 6px;
            justify-content: center;
        }

        .modal-regis input#couponInput {
            color: #dc3f2e;
        }

        .modal-regis .step.active .step-label,
        .modal-regis .step.completed .step-label {
            font-weight: bold;
            color: #105ce4;
        }

        .modal-regis .step-label {
            color: #666;
        }

        .modal-regis .payment-header-t .input-group {
            gap: 8px;
            display: flex;
        }

        .modal-regis button#applyCoupon {
            border-radius: 5px;
            background: #fa8128;
            width: 110px;
            font-size: 16px;
        }

        .modal-regis .payment-header-t {
            padding: 0 20px;
            border-radius: 20px 20px 0px 0px;
            background: linear-gradient(180deg, #fff 0%, #fff4e9 100%);
        }

        .modal-regis .payment-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .modal-regis .progress-steps {
            display: flex;
            justify-content: space-between;
            padding: 20px 0;
            position: relative;
        }

        .modal-regis .progress-line {
            position: absolute;
            top: 34px;
            left: 30px;
            right: 100px;
            height: 1px;
            background-color: #fa8128;
            z-index: 0;
            width: calc(100% - 60px);
        }

        .modal-regis .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 1;
            /* color: #105CE4;
                   */
        }

        .modal-regis .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            background-color: white;
            border: 1px solid #fa8128;
        }

        .modal-regis .step.active .step-circle {
            background-color: #ff8c00;
            border-color: #ff8c00;
            color: white;
        }

        .modal-regis .step.completed .step-circle {
            background-color: #ff8c00;
            border-color: #ff8c00;
            color: white;
        }

        .modal-regis .payment-header {
            margin-bottom: 10px;
        }

        .modal-regis .payment-body {
            padding: 20px 40px;
        }

        .modal-regis .price-info {
            display: flex;
            justify-content: space-between;
            /* margin-bottom: 5px;
                   */
        }

        .modal-regis .total-price {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            margin-top: 10px;
            border-top: 1px dashed #e0e0e0;
        }

        .modal-regis .price {
            color: #ff5722;
            font-weight: bold;
        }

        .modal-regis .payment-methods-container {
            background-color: white;
            padding: 20px 15px;
            border-radius: 15px;
            border: 1px solid #ffd8a8;
        }

        .modal-regis .method-option {
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .modal-regis .method-title {
            /* font-weight: bold;
                   */
            margin-bottom: 15px;
            color: #333;
        }

        .modal-regis .qr-container {
            position: relative;
        }

        .modal-regis .bank-info {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .modal-regis .bank-info-row {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #fff;
            padding: 8px 0;
            color: #333;
        }

        .modal-regis .bank-info-label {
            color: #333;
        }

        .modal-regis .bank-info-value {
            font-weight: 600;
            color: #333;
        }

        .modal-regis .warning-box {
            background-color: #fff9f0;
            border: 1px dashed #ffca80;
            border-radius: 8px;
            padding: 10px;
            margin: 15px 0;
            border: 1px dashed var(--Primary-Color, #fa8128);
            background: #fff9ec;
            font-size: 12px;
            color: #333;
        }

        .modal-regis .warning-icon {
            color: #ff8c00;
            margin-right: 5px;
        }

        .modal-regis .download-btn {
            background-color: #105ce4;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 12px;
        }

        .modal-regis .timer-container {
            background-color: #fff5eb;
            border-radius: 10px;
            padding: 10px 15px;
            /* margin-top: 20px; */
            display: flex;
            justify-content: space-between;
            border-radius: 15px;
            background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
            font-size: 16px;
            color: #333;
        }

        .modal-regis .timer {
            color: #105ce4;
            font-weight: bold;
            font-size: 24px;
        }

        .modal-regis .status-container {
            text-align: center;
            margin-top: 15px;
            color: #666;
        }

        .modal-regis .status-container .loading-icon {
            color: #fa8128;
        }

        .modal-regis .loading-icon {
            display: inline-block;
            margin-left: 5px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .modal-regis .close-btn {
            position: absolute;
            top: 6px;
            right: 10px;
            background: none;
            border: none;
            font-size: 25px;
            cursor: pointer;
            background: #fff;
            /* padding: 5px; */
            /* border: 1px solid #DC3F2E; */
            border-radius: 50%;
            color: #dc3f2e !important;
            font-weight: normal !important;
            width: 30px;
            z-index: 99999;
            height: 30px;
        }

        .modal-regis .nav-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .modal-regis .nav-btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
        }

        .modal-regis .prev-btn {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            color: #666;
        }

        .modal-regis .next-btn {
            background-color: #ffa726;
            border: none;
            color: white;
        }

        .modal-regis .tab-content {
            display: none;
        }

        .modal-regis .tab-content.active {
            display: block;
        }

        .modal-regis .user-info-container {
            background-color: white;
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #ffd8a8;
            background: linear-gradient(to right, #fffcf5, #fff5eb);
            border-radius: 20px;
            border: 2px solid var(--gradient-2, #eb2805);
            background: #fff;
            box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
        }

        .user-info-container h6 {
            font-size: 20px;
            color: #333;
        }

        .modal-regis .user-info-container a {
            color: #fa8128;
            text-decoration: none;
        }

        .modal-regis .success-container {
            background-color: white;
            padding: 40px 20px;
            border-radius: 15px;
            text-align: center;
            /* background: linear-gradient(to right, #fffaf0, #fff5eb);
                   */
        }

        .modal-regis .success-icon {
            width: 65px;
            height: 65px;
            background-color: #4caf50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 40px;
        }

        .modal-regis .success-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .modal-regis .success-text {
            color: #666;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .modal-regis .start-course-btn {
            background: linear-gradient(to right, #ff8a00, #ff5722);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 16px;
            text-transform: uppercase;
            box-shadow: 0 4px 10px rgba(255, 138, 0, 0.2);
            width: 240px;
        }

        .modal-regis .contact-text {
            color: #666;
            margin-top: 20px;
        }

        .modal-regis .hotline {
            color: #ff5722;
            font-weight: bold;
        }

        .modal-regis .coupon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .modal-regis .coupon-btn {
            background-color: white;
            border: 1px dashed #ff5722;
            border-radius: 30px;
            padding: 6px 15px;
            color: #ff5722;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .modal-regis .coupon-btn:hover {
            background-color: #fff5f0;
            transform: translateY(-2px);
        }

        .modal-regis .coupon-tag {
            font-size: 10px;
            color: white;
            background-color: #ff5722;
            padding: 2px 4px;
            border-radius: 2px;
            margin-right: 5px;
        }

        .tab-switch-auth {
            border: none !important;
        }

        @media (max-width: 769px) {
            * {
            }

            .modal-auth .modal-body {
                padding: 20px 15px !important;
            }
        }

        @media (max-width: 500px) {
            * {
            }

            .col-password {
                flex-wrap: wrap;
            }

            .modal-auth .form-wrap {
                width: 100% !important;
            }

            .modal-auth .modal-header {
                font-size: 13px;
            }

            .modal-auth .modal-title {
                font-size: 18px;
            }

            .modal-auth .tab-content {
                padding: 20px 15px;
            }

            .modal-auth .tab {
                height: 51px;
            }

            .modal-auth .tab-switch-auth span {
                font-size: 16px;
            }
        }

    </style>
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/styles_shopee.css') }}"/>
@endpush

@section('content')
    {{--    <header class="header">--}}
    {{--        <div class="container d-flex justify-content-between align-items-center">--}}
    {{--            <img src="{{ asset('assets/frontend/default/images_shopee/header-logo.png')}}" width="240" alt="Kimedia Logo" />--}}
    {{--            <button class="btn">Đăng Nhập</button>--}}
    {{--        </div>--}}
    {{--    </header>--}}

    <section class="hero">
        <div class="container">
            <h1 class="main-heading">
                {{ $course_details->title }}
            </h1>

            <p class="sub-heading">
                {{ $course_details->short_description }}
            </p>
            <div class="action-buttons">

                @if ($enrollment_status)
                    <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                       class="text-center mt-3 start-course-btn m-auto">
                        <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                             style="filter: invert(1);     display: inline-block;"/>
                        VÀO HỌC NGAY!</a>

                @else
                    <button
                        class="buy-course-btn text-text16 text-color-white"
                        type="button"
                        data-bs-toggle="modal"
                        data-bs-target="#modal-regis"
                    >
						<span class="buy-course-btn-icon">
							<img
                                src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                alt="Crown"
                                width="22"
                                height="22"
                            />
						</span>
                        MUA KHÓA HỌC
                    </button>

                    @if(Auth()->check())
                        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                           class="try-free-btn text-color-white">
                        <span class="try-free-btn-icon">
							<img src="{{ asset('assets/frontend/default/images_shopee/coin.svg')}}" alt="Try" width="22"
                                 height="22"/>
						</span>
                            <span style="color: #fff">HỌC THỬ FREE</span>
                        </a>
                    @else
                        <button
                            class="try-free-btn text-color-white"
                            type="button"
                            data-bs-toggle="modal"
                            data-bs-target="#modal-auth"
                        >
						<span class="try-free-btn-icon">
							<img src="{{ asset('assets/frontend/default/images_shopee/coin.svg')}}" alt="Try" width="22"
                                 height="22"/>
						</span>
                            <span>HỌC THỬ FREE</span>
                        </button>
                    @endif
                @endif
            </div>
        </div>
    </section>
    <main class="main">
        <div class="container">
            <div
                class="youtube-video-container"
                data-poster="{{ get_image($course_details->thumbnail) }}"
            >
                @php
                    $preview_url = $course_details->preview;
                    $youtube_regex = '/^(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))((\w|-){11})(?:\S+)?$/';
                    $video_id = '';
                    if (preg_match($youtube_regex, $preview_url, $matches)) {
                        $video_id = $matches[1];
                    }
                @endphp
                @if($video_id)
                    <div class="video-container">
                        <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="{{ $video_id }}">
                        </div>
                    </div>
                @endif

                <div class="solution">
                    <div class="container">
                        <div class="pill">Giải pháp</div>

                        <h2 class="title">Chi tiết khóa học</h2>
                        {{--                    <p class="sub-title">{!! $course_details->short_description !!}</p>--}}
                        <div class="content-course mb-5 sub-title">{!! $course_details->description !!}</div>
                        <div class="shopee-course-interface">
                            <div class="d-flex course-container">

                                @if ($sections->count() > 0)
                                    <div class="course-list">
                                        <div
                                            class="accordion modules-list accordion-flush"
                                            id="course-modules-accordion"
                                        >
                                            @foreach ($sections as $key => $section)
                                                <div class="module-item accordion-item">
                                                    <div
                                                        class="module-header accordion-header d-flex justify-content-between align-items-center"
                                                    >
                                                        <div
                                                            data-bs-toggle="collapse"
                                                            data-bs-target="#course-module-content-{{$key}}"
                                                            aria-expanded="true"
                                                            aria-controls="course-module-content-{{$key}}"
                                                            class=""
                                                        >
                                                            <div class="module-title">
                                                                <h3 class="text-h6 mb-0">
                                                                    {{ ucfirst($section->title) }}
                                                                </h3>
                                                                <div class="module-details">
															<span class="video-count text-text14">
																<img
                                                                    src="{{ asset('assets/frontend/default/images_shopee/video-icon.svg')}}"
                                                                    alt="Video"
                                                                />
																<span>
																	Số lượng video:
																	<strong>7</strong>
																</span>
															</span>
                                                                    <span class="duration text-text14">
																<img
                                                                    src="{{ asset('assets/frontend/default/images_shopee/clock-icon.svg')}}"
                                                                    alt="Clock"
                                                                />
																<span>
																	Thời lượng:
																	<strong>01:06</strong>
																</span>
															</span>
                                                                </div>
                                                            </div>
                                                            <div class="toggle-btn">
                                                                <img
                                                                    class="toggle-btn-icon toggle-btn-icon-collapse"
                                                                    src="{{ asset('assets/frontend/default/images_shopee/minus.svg')}}"
                                                                    alt="Collapse"
                                                                />
                                                                <img
                                                                    class="toggle-btn-icon toggle-btn-icon-expand"
                                                                    src="{{ asset('assets/frontend/default/images_shopee/plus.svg')}}"
                                                                    alt="Expand"
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    @php
                                                        $lessons = DB::table('lessons')
                                                            ->where('section_id', $section->id)
                                                            ->orderBy('sort')
                                                            ->get();
                                                    @endphp

                                                    <div
                                                        class="module-content accordion-collapse collapse {{$key==0?"show":""}}"
                                                        id="course-module-content-{{$key}}"
                                                        data-bs-parent="#course-modules-accordion"
                                                    >
                                                        <div class="accordion-body">
                                                            {{--                                                        <p class="module-content-description">--}}
                                                            {{--                                                            Cách mở gian hàng, cách xác định sản phẩm--}}
                                                            {{--                                                            tiềm năng để bắt đầu bán hàng trên Shopee.--}}
                                                            {{--                                                        </p>--}}

                                                            @foreach ($lessons as $lesson)
                                                                <div class="lesson-item">
                                                                    <a href="javascript:void(0);"
                                                                       class="lesson-link"
                                                                    >
                                                                        <p
                                                                            class="text-text14 lesson-item-title"
                                                                        >
                                                                            {{ ucfirst($lesson->title) }}
                                                                        </p>
                                                                        <div class="d-flex align-items-center">
                                                                            @if($lesson->trial_lesson)
                                                                                <span class="badge free-badge">
																	<img
                                                                        src="{{ asset('assets/frontend/default/images_shopee/coin.svg')}}"
                                                                        width="11"
                                                                        height="11"
                                                                        alt="Coin"
                                                                    />
																	<span>FREE</span>
																</span>
                                                                            @else

                                                                                @if($lesson->hide_title)

                                                                                    <span
                                                                                        class="badge premium-trial-badge">
                                                                                    <img
                                                                                        src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                                        width="12.941px"
                                                                                        height="12.941px" alt="Coin">
                                                                                    <span>FREMIUM TRIAL</span>
                                                                                </span>
                                                                                @else
                                                                                    @if($lesson->paid_lesson)
                                                                                        <span
                                                                                            class="badge pro-trial-badge">
																	<img
                                                                        src="{{ asset('assets/frontend/default/images_shopee/crown-icon.svg')}}"
                                                                        width="12.941px"
                                                                        height="12.941px"
                                                                        alt="Coin"
                                                                    />
																	<span>PRO TRIAL</span>
																</span>
                                                                                    @endif
                                                                                @endif
                                                                                @if($lesson->is_important)
                                                                                    <span class="badge important-badge">
																	<img
                                                                        src="{{ asset('assets/frontend/default/images_shopee/important.svg')}}"
                                                                        width="11"
                                                                        height="11"
                                                                        alt="IMPORTANT"
                                                                    />
																	<span>QUAN TRỌNG</span>
																</span>
                                                                                @endif
                                                                            @endif
                                                                            @if($lesson->duration != '00:00:00' && $lesson->duration != "")
                                                                                <span class="duration-small">
                                                                                <img
                                                                                    src="{{ asset('assets/frontend/default/images_shopee/clock.svg')}}"
                                                                                    alt="Clock"
                                                                                />

                                                                            <span>
                                                                                Thời lượng:
                                                                                <strong>{{$lesson->duration}}</strong>
                                                                            </span>
                                                                            @endif
                                                                        </div>
                                                                    </a>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach

                                        </div>
                                    </div>
                                @endif
                                <div class="course-sale">
                                    <div class="zoom-badge">
                                        <img
                                            src="{{ asset('assets/frontend/default/images_shopee/zoom.png')}}"
                                            width="44"
                                            height="44"
                                            alt=""
                                        />
                                        <span> Học online qua Video </span>
                                    </div>

                                    <div class="register-card">
                                        <h4 class="register-title">ĐĂNG KÝ</h4>
                                        <h3 class="register-subtitle">NHẬN ƯU ĐÃI NGAY!</h3>

                                        <div
                                            class="countdown-container"
                                            data-target-date="2025-06-03"
                                        >
                                            <p class="countdown-text">Đừng bỏ lỡ cơ hội chỉ còn:</p>
                                            <div class="countdow-content">
                                                <div class="countdown-box" id="hours">02</div>
                                                <div class="countdown-separator">:</div>
                                                <div class="countdown-box" id="minutes">14</div>
                                                <div class="countdown-separator">:</div>
                                                <div class="countdown-box" id="seconds">22</div>
                                            </div>
                                        </div>
                                        @php
                                            $price = 0;
                                        @endphp
                                        <div class="price-container">
                                        @if($course_details->discounted_price < $course_details->price)

                                            <!-- Original Price -->
                                                <div class="original-price">{{ number_format($course_details->price) }}đ
                                                </div>

                                                <!-- Discounted Price -->
                                                <div class="current-price-container">
                                                    <span class="current-price-label">CHỈ CÒN:</span>
                                                    <div
                                                        class="current-price">{{ number_format($course_details->discounted_price) }}
                                                        đ
                                                    </div>
                                                </div>
                                            @elseif (isset($course_details->is_paid) && $course_details->is_paid == 0 ||$course_details->discounted_price==0)
                                                <h4 class="g-title">{{ get_phrase('Free') }}</h4>
                                                <div class="current-price-container">
                                                    <div class="current-price">{{ get_phrase('Free') }}</div>
                                                </div>

                                            @elseif (isset($course_details->discount_flag) && $course_details->discount_flag == 1)
                                                @php
                                                    $price = $course_details->discounted_price;
                                                @endphp
                                            <!-- Original Price -->
                                                <div class="original-price">{{ number_format($course_details->price) }}đ
                                                </div>

                                                <!-- Discounted Price -->
                                                <div class="current-price-container">
                                                    <span class="current-price-label">CHỈ CÒN:</span>
                                                    <div
                                                        class="current-price">{{ number_format($course_details->discounted_price) }}
                                                        đ
                                                    </div>
                                                </div>

                                            @else
                                                @php
                                                    $price = $course_details->price;
                                                @endphp
                                                <div class="current-price-container">
                                                    <div
                                                        class="current-price">{{ number_format($course_details->price) }}
                                                        đ
                                                    </div>
                                                </div>
                                            @endif

                                        <!-- Hot Deal Badge -->
                                            {{--                                        @if ($course_details->is_best)--}}
                                            <div class="hot-deal">
                                                <img
                                                    src="{{ asset('assets/frontend/default/images_shopee/hot-deal.png')}}"
                                                    class=" "
                                                    alt="Hot Deal"
                                                />
                                            </div>
                                            {{--                                        @endif--}}

                                        </div>

                                        {{--                                    <div class="info-section">--}}
                                        {{--                                        <p class="info-title">--}}
                                        {{--                                            <img src="{{ asset('assets/frontend/default/images_shopee/gift.svg')}}"--}}
                                        {{--                                                 alt=""/>--}}
                                        {{--                                            <span> Quà tặng kèm: </span>--}}
                                        {{--                                        </p>--}}
                                        {{--                                        <ul class="info-list">--}}
                                        {{--                                            <li class="info-item">--}}
                                        {{--                                                Ebook “Xây dựng thương hiệu cá nhân trên Shopee”--}}
                                        {{--                                                (trị giá 499k)--}}
                                        {{--                                            </li>--}}
                                        {{--                                            <li class="info-item">--}}
                                        {{--                                                Nhóm hỗ trợ riêng trên Facebook/Zalo: Hỏi-đáp--}}
                                        {{--                                                24/7 để tăng tỷ lệ chốt mua khóa học--}}
                                        {{--                                            </li>--}}
                                        {{--                                        </ul>--}}
                                        {{--                                    </div>--}}
                                    </div>

                                    <div class="d-flex action-buttons">

                                        @if ($enrollment_status)
                                            <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                                               class="text-center mt-3 start-course-btn m-auto">
                                                <img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                                    style="filter: invert(1);     display: inline-block;"/>
                                                VÀO HỌC NGAY!</a>

                                        @else
                                            <button
                                                class="buy-course-btn text-color-white"
                                                type="button"
                                                data-bs-toggle="modal"
                                                data-bs-target="#modal-regis"
                                            >
											<span class="buy-course-btn-icon">
												<img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg')}}"
                                                    alt="Crown"
                                                    width="22"
                                                    height="22"
                                                />
											</span>
                                                <span>MUA KHÓA HỌC</span>
                                            </button>
                                            @if(Auth()->check())
                                                <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                                                   class="try-free-btn text-color-white">

											<span class="try-free-btn-icon">
												<img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg')}}"
                                                    alt="Try"
                                                    width="22"
                                                    height="22"
                                                />
											</span>
                                                    <span>HỌC THỬ MIỄN PHÍ</span>

                                                </a>
                                            @else
                                                <button
                                                    class="try-free-btn text-color-white"
                                                    type="button"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#modal-auth"
                                                >
											<span class="try-free-btn-icon">
												<img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg')}}"
                                                    alt="Try"
                                                    width="22"
                                                    height="22"
                                                />
											</span>
                                                    <span>HỌC THỬ MIỄN PHÍ</span>
                                                </button>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @php
                    $faqs = $course_details->faqs ? json_decode($course_details->faqs, true) : [];
                @endphp
                <div class="faq">
                    <div class="container">
                        <div class="shopee-faq-section">
                            @if (count($faqs) > 0)
                                <h1 class="main-title">Câu hỏi thường gặp</h1>

                                <div class="faq-container">
                                    @foreach ($faqs as $key => $faq)
                                        <div class="faq-item {{$key==0?"active":""}}">
                                            <button class="faq-question">
                                                <h2 class="question-text">
                                                    {{ ucfirst($faq['title'] ?? '') }}
                                                </h2>
                                                <div class="toggle-btn">
                                                    <img
                                                        class="toggle-btn-icon toggle-btn-icon-collapse"
                                                        src="{{ asset('assets/frontend/default/images_shopee/minus.svg')}}"
                                                        alt="Collapse"
                                                    />
                                                    <img
                                                        class="toggle-btn-icon toggle-btn-icon-expand"
                                                        src="{{ asset('assets/frontend/default/images_shopee/plus.svg')}}"
                                                        alt="Expand"
                                                    />
                                                </div>
                                            </button>
                                            <div class="faq-answer">
                                                <p>{{ ucfirst($faq['description'] ?? '') }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
    </main>

    <svg id="svg_comp-l4og338c" style="display: none">
        <defs>
            <filter id="faded-comp-l4og338c" color-interpolation-filters="sRGB">
                <feComponentTransfer result="srcRGB"></feComponentTransfer>
                <feColorMatrix type="saturate" values="0.2"></feColorMatrix>
                <feComponentTransfer>
                    <feFuncR type="linear" slope="0.85" intercept="0.08"></feFuncR>
                    <feFuncG type="linear" slope="0.85" intercept="0.08"></feFuncG>
                    <feFuncB type="linear" slope="0.85" intercept="0.08"></feFuncB>
                </feComponentTransfer>
                <feComponentTransfer>
                    <feFuncR type="linear" slope="0.9"></feFuncR>
                    <feFuncG type="linear" slope="0.9"></feFuncG>
                    <feFuncB type="linear" slope="0.9"></feFuncB>
                </feComponentTransfer>
                <feColorMatrix
                    type="matrix"
                    values="0.13725490196078427 0 0 0 0.8627450980392157 0.1333333333333333 0 0 0 0.8666666666666667 0.13725490196078427 0 0 0 0.8627450980392157 0 0 0 1 0"
                ></feColorMatrix>
                <feComponentTransfer></feComponentTransfer>
            </filter>
        </defs>
    </svg>

    {{--    <footer class="footer">--}}
    {{--        <div class="container">--}}
    {{--            <a href="{{ route('home') }}">--}}
    {{--                <img src="{{ get_image(get_frontend_settings('dark_logo')) }}" alt="system logo"--}}
    {{--                     class="object-fit-cover rounded header-dark-logo">--}}
    {{--                <img src="{{ get_image(get_frontend_settings('light_logo')) }}" alt="system logo"--}}
    {{--                     class="object-fit-cover rounded header-light-logo d-none">--}}
    {{--            </a>--}}
    {{--            --}}{{--            <img src="{{ asset('assets/frontend/default/images_shopee/header-logo.png') }}" alt="" />--}}
    {{--        </div>--}}
    {{--    </footer>--}}

    <!-- modal regis -->
    @include('partials.modals.checkout_payment',["offline_payment"=>$offline_payment])

    <!--  modal login -->
    @include('partials.modals.register_login')
@endsection

@push('js')
    <script>
        "use strict";

        function showRightModal(url, header) {
            // SHOWING AJAX PRELOADER IMAGE
            jQuery("#right-modal .modal-body").html(
                '<div class="modal-spinner-border"><div class="spinner-border text-secondary" role="status"></div></div>',
            );
            jQuery("#right-modal .modal-title").html("...");
            // LOADING THE AJAX MODAL
            jQuery("#right-modal").modal("show", {
                backdrop: "true",
            });

            // SHOW AJAX RESPONSE ON REQUEST SUCCESS
            $.ajax({
                url: url,
                success: function (response) {
                    jQuery("#right-modal .modal-title").html(header);
                    jQuery("#right-modal .modal-body").html(response);
                },
            });
        }
    </script>

    <script type="text/javascript">
        "use strict";

        function ajaxModal(url, title, modalClasses = "modal-md", animation = "fade") {
            $("#ajaxModal .modal-dialog").removeClass("modal-sm");
            $("#ajaxModal .modal-dialog").removeClass("modal-md");
            $("#ajaxModal .modal-dialog").removeClass("modal-lg");
            $("#ajaxModal .modal-dialog").removeClass("modal-xl");
            $("#ajaxModal .modal-dialog").removeClass("modal-xxl");
            $("#ajaxModal .modal-dialog").removeClass("modal-fullscreen");
            $("#ajaxModal .modal-dialog").addClass(modalClasses);

            $("#ajaxModal").removeClass("fade");
            $("#ajaxModal").addClass(animation);

            $("#ajaxModal .modal-title").html(title);
            $("#ajaxModal").modal("show");
            $.ajax({
                type: "get",
                url: url,
                success: function (response) {
                    $("#ajaxModal .modal-body").html(response);
                },
            });
        }

        const myModalElModal = document.getElementById("ajaxModal");
        myModalElModal.addEventListener("hidden.bs.modal", (event) => {
            $("#ajaxModal .modal-body").html(
                '<div class="w-100 text-center py-5"><div class="spinner-border my-5" role="status"><span class="visually-hidden"></span></div></div>',
            );
        });

        function videoModal(url, title, modalClasses = "modal-md", animation = "fade") {
            $("#videoModal .modal-dialog").removeClass("modal-sm");
            $("#videoModal .modal-dialog").removeClass("modal-md");
            $("#videoModal .modal-dialog").removeClass("modal-lg");
            $("#videoModal .modal-dialog").removeClass("modal-xl");
            $("#videoModal .modal-dialog").removeClass("modal-xxl");
            $("#videoModal .modal-dialog").removeClass("modal-fullscreen");
            $("#videoModal .modal-dialog").addClass(modalClasses);

            $("#videoModal").removeClass("fade");
            $("#videoModal").addClass(animation);

            $("#videoModal .modal-title").html(title);
            $("#videoModal").modal("show");
            $.ajax({
                type: "get",
                url: url,
                success: function (response) {
                    $("#videoModal .modal-body").html(response);
                },
            });
        }

        const videoModalEl = document.getElementById("videoModal");
        videoModalEl.addEventListener("hidden.bs.modal", (event) => {
            $("#videoModal .modal-body").html(
                '<div class="w-100 text-center py-5"><div class="spinner-border my-5" role="status"><span class="visually-hidden"></span></div></div>',
            );
        });

        function confirmModal(url, elem = false, actionType = null, content = null) {
            $("#confirmModal").modal("show");

            if (elem != false) {
                $.ajax({
                    url: url,
                    success: function (response) {
                        response = JSON.parse(response);
                        //For redirect to another url
                        if (typeof response.success != "undefined") {
                            window.location.href = response.success;
                        }
                        distributeServerResponse(response);
                    },
                });
            } else {
                $("#confirmModal .confirm-btn").attr("href", url);
                $("#confirmModal .confirm-btn").removeAttr("onclick");
            }
        }
    </script>

    <script>
        "use strict";

        function tutorServiceModal(url) {
            // SHOWING AJAX PRELOADER IMAGE
            jQuery("#tutor-service-modal .modal-body").html(
                '<div class="modal-spinner-border"><div class="spinner-border text-secondary" role="status"></div></div>',
            );
            // LOADING THE AJAX MODAL
            jQuery("#tutor-service-modal").modal("show", {
                backdrop: "true",
            });

            // SHOW AJAX RESPONSE ON REQUEST SUCCESS
            $.ajax({
                url: url,
                success: function (response) {
                    jQuery("#tutor-service-modal .modal-body").html(response);
                },
            });
        }
    </script>
    <!-- toster file -->
    <div class="toast-container position-fixed top-0 end-0 p-3"></div>
    <script>
        "use strict";

        function toaster_message(type, icon, header, message) {
            var toasterMessage =
                '<div class="toast ' +
                type +
                ' fade text-12" role="alert" aria-live="assertive" aria-atomic="true" class="rounded-3"><div class="toast-header"> <i class="' +
                icon +
                ' me-2 mt-2px text-14 d-flex"></i> <strong class="me-auto"> ' +
                header +
                ' </strong><small>Vừa mới đây</small><button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button></div><div class="toast-body">' +
                message +
                "</div></div>";
            $(".toast-container").prepend(toasterMessage);
            const toast = new bootstrap.Toast(".toast");
            toast.show();
        }

        function success(message) {
            toaster_message("success", "fi-sr-badge-check", "Thành công!", message);
        }

        function warning(message) {
            toaster_message("warning", "fi-sr-exclamation", "Chú ý !", message);
        }

        function error(message) {
            toaster_message("error", "fi-sr-triangle-warning", "Đã xảy ra lỗi!", message);
        }
    </script>

    <!-- custom scripts -->
    <script>
        "use strict";

        function wishlistToggle(course_id, elem) {
            $.ajax({
                type: "get",
                url: "/toggleWishItem" + "/" + course_id,
                success: function (response) {
                    if (response) {
                        if (response.toggleStatus == "added") {
                            $(elem).addClass("inList");

                            $(elem)
                                .attr("data-bs-title", "Xóa khỏi danh sách ước muốn")
                                .tooltip("dispose")
                                .tooltip("show");

                            success(
                                "Khóa học này đã được thêm vào danh sách mong muốn của bạn.",
                            );
                        } else if (response.toggleStatus == "removed") {
                            $(elem).removeClass("inList");

                            $(elem)
                                .attr("data-bs-title", "Thêm vào danh sách mong muốn")
                                .tooltip("dispose")
                                .tooltip("show");

                            success(
                                "Khóa học này đã được xóa khỏi danh sách mong muốn của bạn.",
                            );
                        }
                    }
                },
            });
        }

        $(document).ready(function () {
            //When need to add a wishlist button inside a anchor tag
            $(".checkPropagation").on("click", function (event) {
                var action = $(this).attr("action");
                var onclickFunction = $(this).attr("onclick");
                var onChange = $(this).attr("onchange");
                var tag = $(this).prop("tagName").toLowerCase();
                console.log(tag);
                if (tag != "a" && action) {
                    $(location).attr("href", $(this).attr("action"));
                    return false;
                } else if (onclickFunction) {
                    if (onclickFunction) {
                        onclickFunction;
                    }
                    return false;
                } else if (tag == "a") {
                    return true;
                }
            });

            // change course layout grid and list in course page
            $(".layout").on("click", function (e) {
                e.preventDefault();
                let layout = $(this).attr("id");

                $.ajax({
                    type: "get",
                    url: "/change/layout",
                    data: {
                        view: layout,
                    },
                    success: function (response) {
                        if (response.reload) {
                            window.location.reload(1);
                        }
                    },
                });
            });

            // toggleWishItems
            $(".toggleWishItem").on("click", function (e) {
                e.stopPropagation();
                e.preventDefault();

                let get_item_id = $(this).attr("id");
                let item_id = get_item_id.split("-");
                item_id = item_id[1];

                const $this = $(this);

                $.ajax({
                    type: "get",
                    url: "https://elearning.topid.vn/toggleWishItem" + "/" + item_id,
                    success: function (response) {
                        if (response) {
                            if (response.toggleStatus == "added") {
                                $this.addClass("inList");
                            } else if (response.toggleStatus == "removed") {
                                $this.removeClass("inList");
                            }
                            window.location.reload(1);
                        }
                    },
                });
            });
        });

        $(function () {
            if ($(".tagify:not(.inited)").length) {
                var tagify = new Tagify(document.querySelector(".tagify:not(.inited)"), {
                    placeholder: "Nhập từ khóa của bạn",
                });
                $(".tagify:not(.inited)").addClass("inited");
            }

            $('[data-bs-toggle="tooltip"]').tooltip();

            //Overlap content start
            document.querySelectorAll(".overlay-content").forEach(function (elem) {
                overlayCollapse(elem);
            });

            function overlayCollapse(elem) {
                if (elem.classList.contains("show-more")) {
                    elem.classList.add("show-less");
                    elem.classList.remove("show-more");
                    elem.querySelector("p a.overlay-action").textContent = "Hiển thị ít hơn - ";
                } else if (elem.classList.contains("show-less")) {
                    elem.classList.add("show-more");
                    elem.classList.remove("show-less");
                    elem.querySelector("p a.overlay-action").textContent = "Hiển thị thêm + ";
                } else {
                    elem.classList.add("show-more");
                    elem.insertAdjacentHTML(
                        "beforeend",
                        '<p><a href="javascript:;" class="overlay-action title text-14px">Hiển thị thêm + </a></p>',
                    );

                    // Select the newly added element and attach the event listener
                    elem.querySelector("p a.overlay-action").addEventListener(
                        "click",
                        function (event) {
                            event.preventDefault();
                            overlayCollapse(elem);
                        },
                    );
                }
            }

            //Overlap content ended
        });
    </script>

    <script>
        "use strict";

        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });
        $(document).ready(function () {
            $(".gSearch-icon").on("click", function () {
                $(".gSearch-show").toggleClass("active");
            });
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Get all FAQ question buttons
            const faqQuestions = document.querySelectorAll(".shopee-faq-section .faq-question");

            // Add click event listener to each question
            faqQuestions.forEach((question) => {
                question.addEventListener("click", function () {
                    // Get the parent FAQ item
                    const faqItem = this.parentElement;

                    // Get the answer element
                    const answer = faqItem.querySelector(".faq-answer");

                    // Check if this FAQ item is already active
                    const isActive = faqItem.classList.contains("active");

                    // Close all FAQ items first
                    document
                        .querySelectorAll(".shopee-faq-section .faq-item")
                        .forEach((item) => {
                            item.classList.remove("active");
                            item.querySelector(".faq-answer").classList.add("collapsed");
                        });

                    // If the clicked item wasn't active, open it
                    if (!isActive) {
                        faqItem.classList.add("active");
                        answer.classList.remove("collapsed");
                    }
                });
            });
        });
    </script>

    <script>
        // Generic function to initialize all YouTube players
        document.addEventListener("DOMContentLoaded", function () {
            const videoContainers = document.querySelectorAll(".youtube-video-container");

            videoContainers.forEach((container) => {
                const videoSrc = container.getAttribute("data-video-src");
                const posterWrapper = container.querySelector(".poster-wrapper");
                const iframeContainer = container.querySelector(".iframe-container");

                // Make the entire poster wrapper clickable
                posterWrapper.addEventListener("click", function () {
                    // Hide poster
                    posterWrapper.style.display = "none";

                    // Show and populate iframe
                    iframeContainer.classList.remove("hidden");
                    iframeContainer.classList.add("active");

                    // Create and add iframe
                    const iframe = document.createElement("iframe");
                    iframe.src = videoSrc;
                    iframe.className = "absolute inset-0 w-full h-full";
                    iframe.setAttribute("frameborder", "0");
                    iframe.setAttribute("allowfullscreen", "");
                    iframe.setAttribute(
                        "allow",
                        "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
                    );

                    iframeContainer.appendChild(iframe);
                });
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Update countdown every second
            function updateCountdown() {
                const currentDate = new Date();

                // Tạo một ngày mới ở thời điểm 00:00 của ngày hôm sau
                const tomorrow = new Date(currentDate);
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0);

                // Tính thời gian còn lại
                const difference = tomorrow - currentDate;

                // Calculate remaining time
                const days = Math.floor(difference / (1000 * 60 * 60 * 24));
                const hours = Math.floor(
                    (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
                );
                const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                // Format numbers to always display with two digits
                // document.querySelector("#days").textContent = days.toString().padStart(2, "0");
                document.querySelector("#hours").textContent = hours
                    .toString()
                    .padStart(2, "0");
                document.querySelector("#minutes").textContent = minutes
                    .toString()
                    .padStart(2, "0");
                document.querySelector("#seconds").textContent = seconds
                    .toString()
                    .padStart(2, "0");
            }

            // Update the countdown immediately and then every second
            updateCountdown();
            setInterval(updateCountdown, 1000);
        });
    </script>
    {{--các modal--}}
    <script>
        $(document).ready(function () {
            // Optimized countdown timer functionality
            function updateCountdown() {
                const countdownElement = $(".countdown-timer");
                if (!countdownElement.length) return;

                const targetDate = new Date(countdownElement.data("countdown"));
                const now = new Date();
                const difference = targetDate - now;

                if (difference <= 0) {
                    // Countdown has ended
                    appendTimerBox("00", "00", "00", "00");
                    $("#countdown-presell").hide();
                    return;
                }

                $("#countdown-presell").show();

                // Calculate time units
                const days = Math.floor(difference / (1000 * 60 * 60 * 24));
                const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((difference % (1000 * 60)) / 1000);

                // Format with leading zeros
                const formattedDays = days < 10 ? "0" + days : days;
                const formattedHours = hours < 10 ? "0" + hours : hours;
                const formattedMinutes = minutes < 10 ? "0" + minutes : minutes;
                const formattedSeconds = seconds < 10 ? "0" + seconds : seconds;

                // Update countdown display
                appendTimerBox(formattedDays, formattedHours, formattedMinutes, formattedSeconds);
            }

            function appendTimerBox(days, hours, minutes, seconds) {
                const countdownElement = $(".countdown-timer");
                countdownElement.html(
                    `<div class='timer-box time-day'>
                <span class='timer-box-number'>${days}</span>
                <span class='timer-box-label'>Ngày</span>
                </div><div class='timer-box time-hour'>
                <span class='timer-box-number'>${hours}</span>
                <span class='timer-box-label'>Giờ</span>
                </div><div class='timer-box time-minute'>
                <span class='timer-box-number'>${minutes}</span>
                <span class='timer-box-label'>Phút</span>
                </div><div class='timer-box time-second'>
                <span class='timer-box-number'>${seconds}</span>
                <span class='timer-box-label'>Giây</span>
                </div>`
                );
            }

            // Update tier activation status based on dates
            function updateTierStatus() {
                const now = new Date();
                let activeFound = false;

                // Check each tier item to determine active status
                $(".tier-item").each(function () {
                    const targetDateStr = $(this).data("targetdate");
                    if (targetDateStr) {
                        const targetDate = new Date(targetDateStr);
                        if (now >= targetDate) {
                            $(this).addClass("active");
                            activeFound = true;
                        } else {
                            $(this).removeClass("active");
                        }
                    }
                });

                // If no tier is active, activate the first one
                if (!activeFound) {
                    $(".tier-item:first").addClass("active");
                }
            }

            // Initialize countdown and update every second
            updateCountdown();
            const countdownInterval = setInterval(updateCountdown, 1000);

            // Initialize tier status
            updateTierStatus();
        });
    </script>
    <script src="{{ asset('assets/frontend/course-shopee/assets/js/source.js') }}"></script>
    <script src="{{ asset('assets/frontend/course-shopee/assets/js/script.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="{{ asset('assets/frontend/default/js/vietqr.js') }}"></script>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>


@endpush
