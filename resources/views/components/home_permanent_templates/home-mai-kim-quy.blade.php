@extends('layouts.default_home_mai_kim_quy')
@section('content')
<style>
	@keyframes pulse-shadow {
		0% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
		100% {
			transform: scale(1);
		}
	}

	.animate-pulse-shadow {
		animation: pulse-shadow 1s infinite;
	}
</style>
<header class="header fixed top-0 left-0 w-full z-50">
	<div class="container mx-auto flex items-center justify-between">
		<a href="/">
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/logo.png') }}"
				alt="KIMEDIA Logo"
				width="165"
				class="h-6 xl:h-9 w-auto"
			/>
		</a>

		<nav class="hidden lg:flex items-center justify-between gap-11 text-lg">
			<ul class="flex gap-x-8 text-white leading-loose font-semibold">
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Trang chủ</a
					>
				</li>
				<li>
					<a
						href="#gioi-thieu"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Giới thiệu</a
					>
				</li>
				<li>
					<a
						href="#khoa-hoc"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Khóa học</a
					>
				</li>
				<li>
					<a
						href="#giang-vien"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Giảng viên</a
					>
				</li>
				<li>
					<a
						href="#danh-gia"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Đánh giá</a
					>
				</li>
				<li>
					<a
						href="#tin-tuc"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Tin tức</a
					>
				</li>
			</ul>
			@if(!Auth()->check())
				<button
					class="!px-2 bg-gradient-to-r from-[#F9D783] to-[#F99D1C] text-[#00238F] leading-normal font-semibold rounded-full flex items-center gap-x-1.5 h-10 w-[158px] border-0 hover:scale-105 hover:shadow-lg transition-all duration-300 hover:from-[#FFBD4D] hover:to-[#F99D1C] login-button"
					data-bs-toggle="modal"
					data-bs-target="#modal-auth"
				>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/icons/user.svg') }}"
						alt=""
						width="28"
						height="28"
						class="w-7 h-7 drop-shadow transition-transform duration-300 group-hover:scale-110"
					/>
					<span class="flex-auto text-center">Đăng nhập</span>
				</button>
			@else
				<!-- User Profile Dropdown -->
				<div class="relative">
					<button
						class="lg:flex hidden bg-white border border-gray-200 text-gray-700 !py-1.5 !pl-1.5 !pr-4 rounded-full items-center hover:bg-gray-50 transition-colors duration-200"
						id="user-menu-button"
						aria-expanded="false"
						data-bs-toggle="dropdown"
					>
						<i class="bi bi-person-circle text-2xl mr-2 {{ Auth()->user()->photo ? 'hidden' : '' }}"></i>
						@if(Auth()->user()->photo)
							<img
								src="{{ get_image(Auth()->user()->photo) }}"
								alt="User Avatar" 
								width="30"
								height="30"
								class="w-[30px] h-[30px] mr-2 object-cover object-center rounded-full"
							/>
						@endif
						<span class="text-sm font-bold">{{ ucfirst(Auth()->user()->name) }}</span>
						<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
									d="M19 9l-7 7-7-7"></path>
						</svg>
					</button>

					<!-- Dropdown Menu -->
					<div
						class="dropdown-menu block absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 !py-2 z-50 hidden"
						id="user-dropdown">
						<!-- User Info Header -->
						<div class="!px-4 !py-3 border-b border-gray-100">
							<div class="flex items-center">
								@if(Auth()->user()->photo)
									<img
										src="{{ get_image(Auth()->user()->photo) }}"
										alt="User Avatar"
										class="w-10 h-10 rounded-full object-cover mr-3"
									/>
								@else
									<i class="bi bi-person-circle text-4xl mr-3"></i>
								@endif
								<div>
									<p class="text-sm font-bold text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
									<p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
									@php
										$enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
									@endphp
									@if($enrollment_status == 'valid')
										<span
											class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
											<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
												<path
													d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
											</svg>
											PRO
										</span>
									@else
										<span
											class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
											FREE
										</span>
									@endif
								</div>
							</div>
						</div>

						<!-- Menu Items -->
						@if (in_array(auth()->user()->role, ['admin', 'instructor']))
							<a href="{{ route(auth()->user()->role . '.dashboard') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
								</svg>
								{{ get_phrase('Dashboard') }}
							</a>
						@endif

						@if (Auth()->user()->role != 'admin')
							<a href="{{ route('my.courses') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
								</svg>
								{{ get_phrase('My Courses') }}
							</a>

							<a href="{{ route('my.profile') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
								</svg>
								{{ get_phrase('My Profile') }}
							</a>

							<a href="{{ route('my.affiliate') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
								</svg>
								{{ get_phrase('Affiliate') }}
							</a>

							<a href="{{ route('wishlist') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
								</svg>
								{{ get_phrase('Wishlist') }}
							</a>

							<a href="{{ route('message') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
								</svg>
								{{ get_phrase('Message') }}
							</a>

							<a href="{{ route('purchase.history') }}"
								class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								{{ get_phrase('Purchase History') }}
							</a>
						@endif

						<div class="border-t border-gray-100 mt-2 !pt-2">
							<a href="{{ route('logout.course') }}"
								class="flex items-center !px-4 !py-2 text-sm text-red-600 hover:bg-red-50">
								<svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
								</svg>
								{{ get_phrase('Log Out') }}
							</a>
						</div>
					</div>
				</div>
			@endif
		</nav>

	<button class="block border-0 lg:hidden menu-toggle bg-transparent">
		<img src="{{ asset('assets/frontend/maikimquy/assets/images/bar.svg') }}" alt="" class="w-6 h-6" />
	</button>
		</div>

	<!-- Mobile Navigation Menu (hidden by default) -->
	<div id="mobile-menu" class="hidden !pt-0 !pb-4 !px-8 w-full z-50">
		<nav
			class="text-sm md:text-base gap-y-2 md:gap-y-4 flex flex-col items-center justify-between"
		>
			<ul
				class="text-center flex flex-col gap-x-8 text-white leading-loose font-semibold"
			>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Trang chủ</a
					>
				</li>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Giới thiệu</a
					>
				</li>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Khóa học</a
					>
				</li>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Giảng viên</a
					>
				</li>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Đánh giá</a
					>
				</li>
				<li>
					<a
						href="#"
						class="text-current hover:text-[#FFBD4D] transition-colors duration-300 relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#FFBD4D] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
						>Tin tức</a
					>
				</li>
			</ul>
			@if(!Auth()->check())
				<button
					class="rounded gap-x-1.5 h-10 w-full bg-gradient-to-r from-[#F9D783] to-[#F99D1C] text-[#00238F] leading-normal font-semibold flex items-center border-0 hover:scale-105 hover:shadow-lg transition-all duration-300 hover:from-[#FFBD4D] hover:to-[#F99D1C] login-button"
					data-bs-toggle="modal"
					data-bs-target="#modal-auth"
				>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/icons/user.svg') }}"
						alt=""
						width="28"
						height="28"
						class="w-7 h-7 drop-shadow transition-transform duration-300 group-hover:scale-110"
					/>
					<span class="flex-auto text-center">Đăng nhập</span>
				</button>
			@else
				<!-- Mobile User Profile -->
				<div class="border-t !pt-3 mt-3">
					<div class="flex items-center mb-3">
						@if(Auth()->user()->photo)
							<img
								src="{{ get_image(Auth()->user()->photo) }}"
								alt="User Avatar"
								class="w-10 h-10 rounded-full object-cover mr-3"
							/>
						@else
							<i class="bi bi-person-circle text-4xl mr-3"></i>
						@endif
						<div>
							<p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
							<p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
						</div>
					</div>

					@if (in_array(auth()->user()->role, ['admin', 'instructor']))
						<a href="{{ route(auth()->user()->role . '.dashboard') }}"
							class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
							{{ get_phrase('Dashboard') }}
						</a>
					@endif

					@if (Auth()->user()->role != 'admin')
						<a href="{{ route('my.courses') }}"
							class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
							{{ get_phrase('My Courses') }}
						</a>
						<a href="{{ route('my.profile') }}"
							class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
							{{ get_phrase('My Profile') }}
						</a>
						<a href="{{ route('wishlist') }}"
							class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
							{{ get_phrase('Wishlist') }}
						</a>
						<a href="{{ route('purchase.history') }}"
							class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
							{{ get_phrase('Purchase History') }}
						</a>
					@endif

					<a href="{{ route('logout.course') }}"
						class="block !py-2 text-sm text-red-600 hover:text-red-700 border-t !pt-2 mt-2">
						{{ get_phrase('Log Out') }}
					</a>
				</div>
			@endif
		</nav>
	</div>
</header>

<section class="hero relative overflow-hidden !pb-5 xl:!pb-0 !pt-16 md:!pt-20 lg:!pt-22">
	<!-- Background decorative elements -->
	<div class="absolute inset-0">
		<img src="{{ asset('assets/frontend/maikimquy/assets/images/hero-bg.png') }}" alt="" class="w-full h-full object-cover" />
	</div>

	<div class="container relative z-10 flex items-center">
		<div
			class="flex flex-col lg:flex-row gap-6 md:gap-8 lg:gap-2.5 items-center w-full"
		>
			<!-- Left Content -->
			<div
				class="w-full lg:w-1/2 xl:w-1/2 text-white"
				data-x-aos="fade-right"
				data-aos-delay="200"
			>
				<!-- Main Heading -->
				<div class="text-center lg:!text-left">
					<h1
						class="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl xl:leading-[1.2] mb-3 md:mb-4 xl:mb-5 gap-x-1 sm:gap-x-2 xl:gap-x-2.5 flex flex-col sm:flex-row items-center justify-center lg:justify-start font-bold"
					>
						<span class="!mb-2 sm:!mb-0"> Hệ sinh thái kinh doanh </span>
		<div
			class="inline-block relative !px-3 sm:!px-4 md:!px-[18px] !py-1 md:!py-1.5"
		>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-left-highlight-bg.svg') }}"
				alt=""
				width="162"
				height="44"
				class="w-full h-auto absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
			/>
			<span
				class="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl xl:leading-[1.2] relative z-[1] inline-block -rotate-3 font-extrabold text-[#072BBA]"
				>tri thức số</span
			>
		</div>
					</h1>
					<div
						class="text-base sm:text-lg md:text-xl xl:text-2xl 2xl:text-3xl mb-2 md:mb-3 xl:mb-3.5 font-extrabold leading-[1.2] text-[#FFBD4D]"
					>
						Lan toả kiến thức. Phát triển kinh doanh. Gia tăng lợi nhuận
					</div>
				</div>

				<!-- Description -->
				<div
					class="text-sm sm:text-base md:text-lg leading-5 md:leading-6 font-normal space-y-2 md:space-y-1 mb-6 md:mb-8 text-center lg:!text-left"
				>
					<p>
						Biến tri thức thành sản phẩm số - Xây dựng thương hiệu cá nhân -
						Phát triển hệ thống đào tạo chuyên nghiệp cùng
						<strong>KIMEDIA</strong>.
					</p>
					<p>
						Dù bạn là cá nhân, chuyên gia hay doanh nghiệp – chúng tôi đều có lộ
						trình phù hợp giúp bạn:
					</p>
				</div>

				<!-- Feature List -->
				<ul
					class="feature-list space-y-3 md:space-y-4 text-sm sm:text-base md:text-lg lg:leading-7 font-bold text-[#00238F]"
				>
	<li
		class="flex items-center space-x-2 md:space-x-3 group hover:text-white hover:scale-105 transition-all duration-300"
	>
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/check-outline.svg') }}"
			width="32"
			height="32"
			alt="Check"
			class="w-6 h-6 md:w-8 md:h-8 shrink-0 grow-0 group-hover:rotate-12 transition-transform duration-300"
		/>
		<div
			class="w-full bg-gradient-to-b from-[#C5DEFF] border border-solid border-white to-white backdrop-blur-sm rounded-full group-hover:shadow-lg group-hover:from-[#B8D4FF] transition-all duration-300"
		>
			<span>Tạo & bán sản phẩm số dễ dàng</span>
		</div>
	</li>
	<li
		class="flex items-center space-x-2 md:space-x-3 group hover:text-white hover:scale-105 transition-all duration-300"
	>
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/check-outline.svg') }}"
			width="32"
			height="32"
			alt="Check"
			class="w-6 h-6 md:w-8 md:h-8 shrink-0 grow-0 group-hover:rotate-12 transition-transform duration-300"
		/>
		<div
			class="w-full bg-gradient-to-b from-[#C5DEFF] border border-solid border-white to-white backdrop-blur-sm rounded-full group-hover:shadow-lg group-hover:from-[#B8D4FF] transition-all duration-300"
		>
			<span>Xây dựng hệ thống học trực tuyến chuyên nghiệp</span>
		</div>
	</li>
	<li
		class="flex items-center space-x-2 md:space-x-3 group hover:text-white hover:scale-105 transition-all duration-300"
	>
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/check-outline.svg') }}"
			width="32"
			height="32"
			alt="Check"
			class="w-6 h-6 md:w-8 md:h-8 shrink-0 grow-0 group-hover:rotate-12 transition-transform duration-300"
		/>
		<div
			class="w-full bg-gradient-to-b from-[#C5DEFF] border border-solid border-white to-white backdrop-blur-sm rounded-full group-hover:shadow-lg group-hover:from-[#B8D4FF] transition-all duration-300"
		>
			<span>Tăng trưởng thương hiệu & doanh thu bền vững</span>
		</div>
	</li>
				</ul>
			</div>

			<!-- Right Content -->
			<div
				class="w-full lg:w-1/2 xl:w-1/2 relative"
				data-x-aos="fade-left"
				data-aos-delay="400"
			>
	<!-- Main Hero Image -->
	<div class="relative max-w-md md:max-w-lg lg:max-w-none mx-auto">
		<!-- Top overlay image -->
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/hero-right-top.png') }}"
			alt="Course preview"
			class="w-full md:w-4/5 lg:w-full xl:w-[558px] h-auto absolute top-0 left-0"
			width="558px"
			height="605px"
		/>
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/hero-right.png') }}"
			alt="Woman with laptop"
			class="w-full md:w-4/5 lg:w-full xl:min-h-[605px]"
		/>

					<!-- Student count badge -->
					<div
						class="student-badge w-4/5 sm:w-3/4 md:w-2/3 lg:w-4/5 xl:w-[350px] group flex items-center justify-between absolute bottom-6 sm:bottom-8 md:bottom-12 left-1/2 -translate-x-1/2 border border-solid border-white bg-gradient-to-b from-white to-[#CDDDF4] rounded-full shadow-lg"
					>
						<div class="text-center">
							<p
								class="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl xl:leading-normal gradient-text font-bold"
							>
								100.000+
							</p>
							<p
								class="text-xs sm:text-sm leading-normal text-[#002084] font-bold"
							>
								HỌC VIÊN
							</p>
						</div>

		<div
			class="flex -space-x-2 sm:-space-x-3 md:-space-x-4 lg:-space-x-5"
		>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-1.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-2.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-3.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-4.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-5.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/hero-st-6.png') }}"
				alt="Student"
				width="53"
				height="53"
				class="size-8 sm:size-10 md:size-12 lg:size-[52px] object-cover rounded-full border-1 sm:border-2 border-white"
			/>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<section id="gioi-thieu" class="about !pb-20 !pt-10 md:!pb-20 xl:!pt-20 xl:!pb-36 relative overflow-hidden">
	<!-- Background decorative elements -->
	<div class="absolute inset-0">
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/about-bg.svg') }}"
			alt=""
			class="absolute inset-0 w-full h-full object-cover"
		/>
	</div>

	<div class="container relative z-10">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-3 md:gap-5 lg:gap-11 items-center">
	<!-- Left Content -->
	<div class="relative" data-x-aos="fade-right" data-aos-delay="200">
		<img
			src="{{ asset('assets/frontend/maikimquy/assets/images/about-left.png') }}"
			alt="KIMEDIA Team"
			class="w-full max-w-lg mx-auto drop-shadow-2xl"
		/>
	</div>

			<!-- Right Content -->
			<div
				class="space-y-0 md:space-y-1 lg:space-y-8"
				data-x-aos="fade-left"
				data-aos-delay="400"
			>
				<!-- Main Heading -->
				<h2
					class="text-lg md:text-xl lg:text-4xl !mb-2 md:!mb-4 lg:!mb-6 xl:text-[42px] !leading-[initial] font-extrabold gradient-text"
				>
					VỀ KIMEDIA
				</h2>

				<!-- Description -->
				<div
					class="text-sm md:text-base xl:text-lg xl:leading-6 xl:space-y-5 text-[#212121] font-normal text-justify"
				>
					<p>
						Được thành lập vào tháng 6 năm 2021,
						<span class="text-[#1540FF] font-bold"> KIMEDIA </span> ra đời với
						sứ mệnh “<span class="text-[#1540FF] font-bold"
							>Make Education Easier</span
						>” – mang lại giải pháp giáo dục dễ tiếp cận hơn cho mọi cá nhân và
						doanh nghiệp trong kỷ nguyên số.
					</p>
					<p>
						Trong suốt hơn 4 năm hình thành và phát triển, KIMEDIA không ngừng
						mở rộng hệ sinh thái đào tạo trực tuyến, tập trung vào việc giúp
						người học và nhà đào tạo xây dựng, vận hành hiệu quả các hệ thống
						khóa học online hiện đại và có tính ứng dụng cao.
					</p>
				</div>

				<!-- CTA Button -->
				<div
					class="!px-6 !py-1.5 !mt-2 lg:!mt-4 w-fit text-sm md:text-base xl:text-lg rounded-full text-white bg-gradient-to-r from-[#1540FF] to-[#00238F]"
				>
					Cung cấp đa dạng các khóa học & giải pháp như:
				</div>

				<!-- Feature List -->
				<div
					class="!mt-3 md:!mt-5 xl:!mt-7 text-sm md:text-base xl:text-lg font-normal leading-6 text-[#212121] text-justify"
				>
					<div class="flex items-start space-x-2">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
							alt=""
							width="25"
							height="25"
							class="size-4 md:size-5 lg:size-[25px] mt-0.5"
						/>
						<span
							>Hệ thống khóa học online thực chiến với nhiều lĩnh vực (Video/
							Webinar)</span
						>
					</div>
					<div class="flex items-start space-x-2">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
							alt=""
							width="25"
							height="25"
							class="size-4 md:size-5 lg:size-[25px] mt-0.5"
						/>
						<span>Digital marketing (Facebook, Google, TikTok Ads...)</span>
					</div>
					<div class="flex items-start space-x-2">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
							alt=""
							width="25"
							height="25"
							class="size-4 md:size-5 lg:size-[25px] mt-0.5"
						/>
						<span
							>Tư vấn chiến lược bán hàng và tự động hóa quy trình (Automation
							Marketing)</span
						>
					</div>
				</div>
			</div>
		</div>

		<!-- Statistics Cards -->
		<div
			class="mt-6 md:mt-10 lg:mt-20 gap-8 md:gap-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
		>
			<!-- Card 1 -->
			<div
				class="relative !pt-3 md:!pt-4 !pl-3 md:!pl-3.5 !pb-3 !pr-3 hover:scale-105 hover:-translate-y-2 transition-all duration-300 cursor-pointer group"
				data-x-aos="fade-up"
				data-aos-delay="200"
			>
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/about-card-bg.svg') }}"
					alt=""
					class="absolute top-0 left-0 max-h-full w-full h-auto lg:min-h-full object-cover group-hover:drop-shadow-lg transition-all duration-300"
				/>
				<div class="relative z-10">
					<h3
						class="text-2xl sm:text-3xl md:text-4xl xl:text-5xl xl:leading-[1.17] mb-2 xl:mb-3 font-bold gradient-text group-hover:scale-110 transition-transform duration-300"
					>
						100.000+
					</h3>
					<p
						class="text-sm sm:text-base md:text-lg leading-6 md:leading-9 text-[#00238F] font-normal"
					>
						Học viên trên nền tảng
					</p>
				</div>
			</div>

			<!-- Card 2 -->
			<div
				class="relative !pt-3 md:!pt-4 !pl-3 md:!pl-3.5 !pb-3 !pr-3 hover:scale-105 hover:-translate-y-2 transition-all duration-300 cursor-pointer group sm:col-span-2 md:col-span-1"
				data-x-aos="fade-up"
				data-aos-delay="400"
			>
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/about-card-bg.svg') }}"
					alt=""
					class="absolute top-0 left-0 max-h-full w-full h-auto lg:min-h-full object-cover group-hover:drop-shadow-lg transition-all duration-300"
				/>
				<div class="relative z-10">
					<h3
						class="text-2xl sm:text-3xl md:text-4xl xl:text-5xl xl:leading-[1.17] mb-2 xl:mb-3 font-bold gradient-text group-hover:scale-110 transition-transform duration-300"
					>
						200+
					</h3>
					<p
						class="text-sm sm:text-base md:text-lg leading-6 md:leading-9 text-[#00238F] font-normal"
					>
						Khóa học được sản xuất và đưa ra ngoài thị trường
					</p>
				</div>
			</div>

			<!-- Card 3 -->
			<div
				class="relative !pt-3 md:!pt-4 !pl-3 md:!pl-3.5 !pb-3 !pr-3 hover:scale-105 hover:-translate-y-2 transition-all duration-300 cursor-pointer group sm:col-span-2 md:col-span-1"
				data-x-aos="fade-up"
				data-aos-delay="600"
			>
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/about-card-bg.svg') }}"
					alt=""
					class="absolute top-0 left-0 max-h-full w-full h-auto lg:min-h-full object-cover group-hover:drop-shadow-lg transition-all duration-300"
				/>
				<div class="relative z-10">
					<h3
						class="text-2xl sm:text-3xl md:text-4xl xl:text-5xl xl:leading-[1.17] mb-2 xl:mb-3 font-bold gradient-text group-hover:scale-110 transition-transform duration-300"
					>
						500+
					</h3>
					<p
						class="text-sm sm:text-base md:text-lg leading-6 md:leading-9 text-[#00238F] font-normal"
					>
						Chuyên gia, nhà đào tạo trong cộng đồng kinh doanh tri thức
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<section
	class="course relative !py-12 md:!py-16 lg:!py-20 bg-repeat bg-center"
	style="background-image: url('{{ asset('assets/frontend/maikimquy/assets/images/course-bg.png') }}')"
>
	<h2
		class="w-full sm:w-fit !px-6 sm:!px-10 md:!px-14 !pt-2 sm:!pt-3 md:!pt-3.5 !pb-1.5 md:!pb-2 text-lg md:text-xl lg:text-3xl xl:text-[42px] leading-tight md:leading-[1.36] absolute left-1/2 bottom-full -translate-x-1/2 text-center text-white font-extrabold bg-[#00238F]"
		style="border-radius: 80px 80px 0px 0px"
	>
		KHÓA HỌC NỔI BẬT
	</h2>
	<div class="container relative z-10">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-10 items-center">
			<!-- Left Content - Course Card -->
			<div
				class="!order-2 lg:!-order-none"
				data-x-aos="fade-right"
				data-aos-delay="200"
			>
				<!-- Course Header -->
				<div class="!text-center lg:!text-left">
					<div class="relative z-10 mb-6 md:mb-8">
						<div
							class="text-[#00238F] text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] xl:leading-tight font-extrabold"
						>
							<h3 class="[font:inherit]">BỘ KHÓA HỌC</h3>
							<div
								class="flex items-center justify-center lg:justify-start space-x-1 sm:space-x-2"
							>
								<span> ELEARNING </span>
								<div
									class="!px-4 sm:!px-6 md:!px-9 !py-1 sm:!py-1.5 md:!py-2 relative"
								>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/hero-left-highlight-bg.svg') }}"
										alt=""
										width="162"
										height="44"
										class="w-full h-auto absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
									/>
									<span
										class="relative z-[1] inline-block text-lg sm:text-xl md:text-2xl leading-[1.2] -rotate-3"
										>Master</span
									>
								</div>
							</div>
						</div>
						<p
							class="text-lg sm:text-xl md:text-2xl leading-[1.4] text-[#1540FF] font-bold !mt-2 md:!mt-3 lg:!mt-4"
						>
							X100 HỌC VIÊN SAU 30 NGÀY
						</p>
					</div>

					<!-- Course Details -->
					<div
						class="space-y-3 md:space-y-4 text-sm sm:text-base md:text-lg xl:text-lg xl:leading-6 text-[#212121] font-normal"
					>
						<!-- Registration Period -->
						<div
							class="flex items-center lg:items-start space-x-3 md:space-x-5"
						>
							<img
								src="{{ asset('assets/frontend/maikimquy/assets/images/calendar.svg') }}"
								alt=""
								width="49"
								height="49"
								class="size-8 sm:size-10 md:size-12 shrink-0"
							/>
							<div class="flex-auto">
								<p class="font-bold">Thời gian đăng ký:</p>
								<p>Từ 01/06 đến hết 30/06/2025</p>
							</div>
						</div>

						<!-- Course Format -->
						<div
							class="flex items-center lg:items-start space-x-3 md:space-x-5"
						>
							<img
								src="{{ asset('assets/frontend/maikimquy/assets/images/video.svg') }}"
								alt=""
								width="49"
								height="49"
								class="size-8 sm:size-10 md:size-12 shrink-0"
							/>
							<div class="flex-auto">
								<p class="font-bold">Hình thức:</p>
								<p>
									Online - Học qua video, livestream & tư vấn 1-1 trực
									tiếp
								</p>
							</div>
						</div>

						<!-- Instructors -->
						<div
							class="flex items-center lg:items-start space-x-3 md:space-x-5"
						>
							<img
								src="{{ asset('assets/frontend/maikimquy/assets/images/user.svg') }}"
								alt=""
								width="49"
								height="49"
								class="size-8 sm:size-10 md:size-12 shrink-0"
							/>
							<div class="flex-auto">
								<p class="font-bold">Chuyên gia đào tạo:</p>
								<p>Mai Kim Quy & Hiếu Nguyễn</p>
							</div>
						</div>
					</div>

					<!-- Special Offer -->
					<div
						class="!p-5 md:!p-6 md:!pt-8 xl:!pt-11 mt-5 md:mt-10 lg:mt-16 relative bg-gradient-to-b from-[#CCE1FF] to-[#E6F0FF] rounded-3xl text-white"
					>
						<div class="text-left lg:text-center">
							<p
								class="text-base md:text-lg lg:text-xl absolute top-0 left-4 -translate-y-1/2 bg-gradient-to-r from-[#1540FF] to-[#00238F] w-fit rounded-full !px-6 !py-1.5 font-bold text-white"
							>
								<span> Ưu đãi đặc biệt - Giới hạn </span>
								<span class="text-[#FFBD4D]"> 15 slots! </span>
							</p>
							<div
								class="space-y-2 text-sm md:text-base lg:text-lg font-normal leading-6 text-[#212121]"
							>
								<div class="flex items-center justify-start space-x-2">
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
										alt=""
										width="25"
										height="25"
										class="size-4 md:size-5 lg:size-6 object-cover"
									/>
									<span
										>Giảm ngay
										<span class="font-bold text-[#1540FF]"
											>3.500.000đ</span
										>
										học phí</span
									>
								</div>
								<div class="flex items-center justify-start space-x-2">
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
										alt=""
										width="25"
										height="25"
										class="size-4 md:size-5 lg:size-6 object-cover"
									/>
									<span
										>Tặng bộ quà tặng trị giá
										<span class="font-bold text-[#1540FF]"
											>3.000.000đ</span
										></span
									>
								</div>
								<div class="flex items-center justify-start space-x-2">
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
										alt=""
										width="25"
										height="25"
										class="size-4 md:size-5 lg:size-6 object-cover"
									/>
									<span
										><span class="font-bold text-[#1540FF]"
											>Miễn phí</span
										>
										1 buổi tư vấn 1-1 cùng chuyên gia của KIMEDIA</span
									>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Right Content - Course Benefits -->
			<div
				class="bg-gradient-to-r from-[#1540FF] to-[#00238F] rounded-md md:rounded-xl lg:rounded-3xl !px-6 !pt-7 !pb-8 text-white"
				data-x-aos="fade-left"
				data-aos-delay="400"
			>
				<div class="space-y-6">
					<!-- Header -->
					<p
						class="text-sm md:text-base lg:text-lg text-white leading-6 font-normal mb-2 md:mb-4"
					>
						<strong> Elearning Master </strong>
						là chương trình đào tạo toàn diện, được thiết kế dành cho những ai
						muốn tạo ra và vận hành hệ thống khóa học online chuyên nghiệp.
					</p>
					<div class="mt-1.5 xl:mb-[18px] flex items-center justify-center">
						<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/arrow-down.png') }}"
						alt=""
						width="42"
						height="50"
						class="w-auto h-9 md:h-10 lg:h-[50px] animate-bounce"
						data-aos-lazy="fade-down"
						data-aos-delay="300"
						data-aos-duration="1000"
						data-aos-repeat="true"
					/>
					</div>

					<!-- Benefits List -->
					<div
						class="rounded md:rounded-lg lg:rounded-3xl !pt-[18px] !px-6 !pb-8 text-sm md:text-base lg:text-lg bg-gradient-to-b from-white to-[#CCE1FF] font-medium text-[#212121]"
					>
						<h4
							class="text-sm md:text-lg lg:text-xl !px-6 !py-1.5 mb-4 mx-auto bg-gradient-to-r from-[#1540FF] to-[#00238F] w-fit rounded-full font-bold text-white"
						>
							Nội dung nổi bật của khóa học:
						</h4>
						<ul class="space-y-3 !pl-6 [list-style:square]">
							<li class="list-item items-start space-x-3">
								<span>Nắm trọn quy trình tạo khóa học online từ A-Z</span>
							</li>
							<li class="list-item items-start space-x-3">
								<span>Bứt phá doanh thu – không chỉ tăng mà tăng MẠNH</span>
							</li>
							<li class="list-item items-start space-x-3">
								<span>Chạy quảng cáo – hút học viên siêu tốc</span>
							</li>
							<li class="list-item items-start space-x-3">
								<span
									>Bán nhiều lần - Biến 1 khóa học thành nguồn thu dài
									hạn</span
								>
							</li>
							<li class="list-item items-start space-x-3">
								<span
									>Tự tay lên chiến lược bán khóa học như chuyên gia</span
								>
							</li>
							<li class="list-item items-start space-x-3">
								<span>
									Tối ưu thời gian – tiết kiệm chi phí đến mức tối đa
								</span>
							</li>
							<li class="list-item items-start space-x-3">
								<span>Dựng website học online như dân chuyên</span>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>

		<!-- CTA Button -->
		<div class="text-center mt-12">
			<a
				href="#khoa-hoc"
				class="animate-pulse-shadow text-base md:text-lg lg:text-xl xl:text-2xl xl:leading-8 flex items-center gap-4 mx-auto w-fit bg-[#FFBD4D] hover:bg-orange-600 text-[#00238F] font-bold !py-2.5 !px-6 rounded-full border-0 transition-colors duration-300 shadow-lg"
			>
				<span> Đăng ký ngay để giữ suất ưu đãi </span>
				<img src="{{ asset('assets/frontend/maikimquy/assets/images/arrow-right.svg') }}" alt="" />
			</a>
		</div>
	</div>

	<div class="container">
		<!-- Course Listing Section -->
		<div class="mt-10 xl:mt-[52px]">
			<!-- Section Title -->
			<h2
				class="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] !leading-[initial] text-center font-extrabold gradient-text mb-4 md:mb-8 xl:mb-11"
				data-x-aos="fade-up"
			>
				DANH SÁCH KHÓA HỌC
			</h2>

			<!-- Course Cards Grid -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-7 mb-12">
				@if(isset($courses) && $courses->count()>0)
					@foreach($courses as $course)
						<div
							class="shadow-[0px_0px_20px_0px_rgba(0,0,0,0.20)] p-3 !pb-3.5 rounded-[30px] bg-white relative overflow-hidden hover:scale-105 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
							data-x-aos="fade-up"
							data-aos-delay="200"
						>
							<!-- Course Image -->
							<div class="mb-4">
								<img
								 src="{{ get_image($course->thumbnail) }}"
                                alt="{{$course->title}}"
								class="w-full aspect-square object-cover rounded-[20px] group-hover:scale-105 transition-transform duration-300"
							/>
							</div>

							<!-- Course Description -->
							<h4
								class="text-sm md:text-base lg:text-lg leading-6 text-center font-bold text-[#00238F] line-clamp-2"
							>
								{{$course->title}}
							</h4>
							<!-- View Details Button -->
							<a
								href="{{ route('course.details', $course->slug) }}"
								class="!py-1.5 !pl-7 !pr-5 bg-[#e5eaff] rounded-full border border-solid border-white text-[#1540FF] text-base leading-normal font-semibold mx-auto w-fit flex items-center gap-1 mt-4 hover:bg-[#d0dfff] hover:scale-105 transition-all duration-300"
								style="
									box-shadow: 0px 0px 5.48px 0px rgba(96, 116, 218, 0.46) inset;
								"
							>
								<span>Xem chi tiết</span>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
									alt=""
									width="15"
									height="15"
									class="w-4 h-4 hover:translate-x-1 transition-transform duration-300"
								/>
							</a>
						</div>
					@endforeach
				@endif
			</div>

			<!-- View All Courses Button -->
			<!-- CTA Button -->
			<div class="text-center mt-12">
				<a
					href="{{ route('courses') }}"
					class="flex items-center gap-4 mx-auto w-fit bg-[#FFBD4D] hover:bg-orange-600 text-[#00238F] font-bold !py-2.5 !px-6 rounded-full border-0 text-base md:text-lg lg:text-xl xl:text-2xl xl:leading-8 transition-colors duration-300 shadow-lg"
				>
					<span> Xem toàn bộ khóa học </span>
				<img src="{{ asset('assets/frontend/maikimquy/assets/images/arrow-right.svg') }}" alt="" />
				</a>
			</div>
		</div>
	</div>
</section>

<section
	id="giang-vien"
	class="professional relative !py-12 bg-no-repeat bg-cover bg-center overflow-hidden"
	style="background-image: url({{ asset('assets/frontend/maikimquy/assets/images/professional-bg.png') }})"
>
	<div class="container">
		<!-- Section Title -->
		<h2
			class="text-center text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] !leading-[inherit] font-extrabold gradient-text !mb-4 md:!mb-6 xl:!mb-8"
			data-x-aos="fade-up"
		>
			CHUYÊN GIA ĐỒNG HÀNH
		</h2>

		<!-- Section Description -->
		<p
			class="!mb-10 md:!mb-14 lg:!mb-18 xl:!mb-[75px] text-sm md:text-base xl:text-lg leading-6 text-center xl:max-w-[751px] text-[#212121] mx-auto"
			data-x-aos="fade-up"
			data-aos-delay="200"
		>
			Đồng hành cùng bạn trong khóa học là các chuyên gia của KIMEDIA đã có nhiều năm
			kinh nghiệm thực chiến trong ngành, liên tục hỗ trợ cho học viên và là đội tác
			đảo tạo/tư vấn của nhiều doanh nghiệp lớn.
		</p>

		<!-- Team Members Grid -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
			<!-- Team Member 1: Mai Kim Quý -->
			<div
				class="bg-white rounded md:rounded-md xl:rounded-[10px] !p-6 border border-white border-solid shadow-[0px_4px_15px_0px_rgba(0,35,143,0.05)] relative hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				data-x-aos="fade-right"
				data-aos-delay="400"
			>
				<!-- Profile Image with Stamp Border -->
				<div class="flex items-stretch !mb-6 xl:!mb-12">
					<div class="flex-auto">
						<div class="mb-2">
							<span
								class="text-sm md:text-base xl:text-lg leading-normal font-normal text-[#00238F] mb-0.5"
								>Chuyên gia</span
							>
							<h3
								class="text-xl sm:text-2xl xl:text-[34px] xl:leading-10 font-extrabold gradient-text group-hover:scale-105 transition-transform duration-300"
							>
								Mai Kim Quý
							</h3>
						</div>
						<div
							class="text-lg !px-3.5 !py-0.5 bg-gradient-to-r from-[#1540FF] to-[#00238F] w-fit rounded-full font-bold text-white group-hover:scale-105 transition-transform duration-300"
						>
							Founder của KIMEDIA
						</div>
					</div>
					<div
						class="w-20 basis-20 md:basis-[200px] md:w-[200px] xl:basis-[240px] xl:w-[240px] relative grow-0 shrink-0"
					>
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/professional-1.png') }}"
							alt="Mai Kim Quý"
							width="175.238px"
							height="188.429px"
							class="absolute bottom-8 max-w-none md:max-w-full md:-bottom-7 xl:-bottom-10 -right-4 w-24 md:w-full h-auto object-cover group-hover:scale-110 transition-transform duration-300"
						/>
					</div>
				</div>

				<!-- Experience List -->
				<ul
					class="text-sm md:text-base list-disc space-y-3 !pl-4 xl:!pl-8 text-[#212121]"
				>
					<li class="list-item">
						<span
							>Hơn 9 năm kinh nghiệm trong Digital Marketing: quảng cáo FB,
							quảng cáo GG, quảng cáo tiktok... </span
						>
					</li>
					<li class="list-item">
						<span
							>4+ năm kinh doanh online với kinh nghiệm setup cửa hàng, quản
							lý vận hành</span
						>
					</li>
					<li class="list-item">
						<span
							>Kinh nghiệm trong đa ngành hàng: thời trang, giáo dục, mỹ phẩm,
							công nghệ, Spa, sự kiện...</span
						>
					</li>
					<li class="list-item">
						<span
							>Founder KIMEDIA Giáo dục trực tuyến: Engmates, FEDU, MSTs, Yoga
							Đẹp… Marketing Agency</span
						>
					</li>
					<li class="list-item">
						<span
							>Hiện đang là đối tác và giảng viên của Get Course - một công ty
							hàng đầu về giáo dục</span
						>
					</li>
				</ul>
			</div>

			<!-- Team Member 2: Hiếu Nguyễn -->
			<div
				class="bg-white rounded md:rounded-md xl:rounded-[10px] !p-6 shadow-lg relative hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				data-x-aos="fade-left"
				data-aos-delay="600"
			>
				<!-- Profile Image with Stamp Border -->
				<div class="flex items-stretch !mb-6 xl:!mb-12">
					<div class="flex-auto">
						<div class="mb-2">
							<span
								class="text-sm md:text-base xl:text-lg leading-normal font-normal text-[#00238F] mb-0.5"
								>Chuyên gia</span
							>
							<h3
								class="text-xl sm:text-2xl xl:text-[34px] xl:leading-10 font-extrabold gradient-text group-hover:scale-105 transition-transform duration-300"
							>
								Hiếu Nguyễn
							</h3>
						</div>
						<div
							class="text-lg !px-3.5 !py-0.5 bg-gradient-to-r from-[#1540FF] to-[#00238F] w-fit rounded-full font-bold text-white group-hover:scale-105 transition-transform duration-300"
						>
							Giám đốc dự án
						</div>
					</div>
					<div
						class="w-20 basis-20 md:basis-[200px] md:w-[200px] xl:basis-[240px] xl:w-[240px] relative grow-0 shrink-0"
					>
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/professional-2.png') }}"
							alt="Hiếu Nguyễn"
							width="175.238px"
							height="188.429px"
							class="absolute bottom-8 max-w-none md:max-w-full md:-bottom-7 xl:-bottom-10 -right-4 w-24 md:w-full h-auto object-cover group-hover:scale-110 transition-transform duration-300"
						/>
					</div>
				</div>

				<!-- Experience List -->
				<ul
					class="text-sm md:text-base list-disc space-y-3 !pl-4 xl:!pl-8 text-[#212121]"
				>
					<li class="list-item">
						<span
							>8 Năm kinh nghiệm quản lý Marketing tại các công ty công nghệ,
							giáo dục</span
						>
					</li>
					<li class="list-item">
						<span>8 năm Copywriter và tư vấn thương hiệu</span>
					</li>
					<li class="list-item">
						<span>4 năm Quản lý sale và Marketing</span>
					</li>
					<li class="list-item">
						<span
							>Giảng viên đóng gói khóa học và quảng cáo trực tuyến của Get
							Academy thị trường Vietnam</span
						>
					</li>
				</ul>
			</div>
		</div>
	</div>
</section>

<section
	id="danh-gia"
	class="feedback relative !pt-16 !pb-20 overflow-hidden bg-center bg-cover"
	style="background-image: url({{ asset('assets/frontend/maikimquy/assets/images/feedback-bg.svg') }})"
>
	<div  class="container relative z-10">
		<!-- Section Title -->
		<div class="text-center relative mb-14 w-fit mx-auto" data-x-aos="fade-up">
			<img
				src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-title-icon.png') }}"
				alt="Graduation cap"
				width="72"
				height="50"
				class="xl:w-[72px] h-auto absolute top-0 left-0 -translate-x-1/4 -translate-y-1/3"
			/>
			<h2
				class="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[42px] leading-tight font-bold text-white"
			>
				NHIỀU CHUYÊN GIA<br />
				ĐÃ ỨNG DỤNG THÀNH CÔNG
			</h2>
		</div>

		<!-- Testimonials Grid -->
		<div
			class="slider-container [&>.slick-list]:h-full [&_.slick-track]:h-full [&>.slick-list]:overflow-y-visible"
			data-x-aos="fade-up"
			data-aos-delay="200"
		>
			<!-- Testimonial 1: Nguyễn Thị Minh Trang -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-1-avatar.png') }}"
					alt="Nguyễn Thị Minh Trang"
					width="112"
					height="112"
					class="absolute -top-6 -left-10 size-28 rounded-full object-cover transition-transform duration-300"
					style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
				/>
					<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
					alt=""
					srcset=""
					width="25"
					height="32"
					class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
				/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Nguyễn Thị Minh Trang
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								32 tuổi, giáo viên tiếng Anh tự do
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Trước đây mình chỉ dạy offline, rất mệt vì đi chuyến và không mở
							rộng được học viên. Nhờ khóa Elearning Master, mình đã tự quay
							được video bài giảng, dựng được website riêng và có hơn 200 học
							viên chỉ sau 3 tháng. Không ngờ việc dạy học online lại hiệu quả
							đến vậy!
						</p>
					</div>
				</div>
			</div>

			<!-- Testimonial 2: Trần Hoàng Duy -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-2-avatar.png') }}"
					alt="Trần Hoàng Duy"
					width="112"
					height="112"
					class="absolute -top-6 -left-10 size-28 rounded-full object-cover transition-transform duration-300"
					style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
				/>
					<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
					alt=""
					srcset=""
					width="25"
					height="32"
					class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
				/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Trần Hoàng Duy
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								28 tuổi, huấn luyện viên thể hình
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
										alt=""
										width="16"
										height="16"
										class="inline-block w-4 h-4"
									/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Mình có chuyên môn nhưng không biết làm sao để đưa khóa học ra
							thị trường. Nhờ khóa Bán hàng qua khóa học, mình hiểu được cách
							định giá, xây dựng ưu đãi và dùng email marketing để chăm sóc
							học viên. Hiện tại, doanh thu từ khóa học online của mình gấp 3
							lần so với việc dạy offline.
						</p>
					</div>
				</div>
			</div>

			<!-- Testimonial 3: Lê Hồng Nhung -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-3-avatar.png') }}"
					alt="Lê Hồng Nhung"
					width="112"
					height="112"
					class="absolute -top-6 -left-10 size-28 rounded-full object-cover"
					style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
				/>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
						alt=""
						srcset=""
						width="25"
						height="32"
						class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
					/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Lê Hồng Nhung
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								38 tuổi, trưởng phòng thiết kế
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Chúng tôi đã triển khai thành công sử dụng hình ảnh và video
							bằng AI với khóa học: Thiết kế Hình ảnh & Video 5s siêu thực với
							AI của KIMEDIA. Giờ các nhân sự đều biết làm ảnh và video nhanh
							chóng, hiệu quả để sử dụng cho các chiến dịch quảng cáo.
						</p>
					</div>
				</div>
			</div>
			<!-- Testimonial 1: Nguyễn Thị Minh Trang -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-1-avatar.png') }}"
						alt="Nguyễn Thị Minh Trang"
						width="112"
						height="112"
						class="absolute -top-6 -left-10 size-28 rounded-full object-cover transition-transform duration-300"
						style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
					/>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
						alt=""
						srcset=""
						width="25"
						height="32"
						class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
					/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Nguyễn Thị Minh Trang
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								32 tuổi, giáo viên tiếng Anh tự do
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Trước đây mình chỉ dạy offline, rất mệt vì đi chuyến và không mở
							rộng được học viên. Nhờ khóa Elearning Master, mình đã tự quay
							được video bài giảng, dựng được website riêng và có hơn 200 học
							viên chỉ sau 3 tháng. Không ngờ việc dạy học online lại hiệu quả
							đến vậy!
						</p>
					</div>
				</div>
			</div>

			<!-- Testimonial 2: Trần Hoàng Duy -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-2-avatar.png') }}"
						alt="Trần Hoàng Duy"
						width="112"
						height="112"
						class="absolute -top-6 -left-10 size-28 rounded-full object-cover transition-transform duration-300"
						style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
					/>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
						alt=""
						srcset=""
						width="25"
						height="32"
						class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
					/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Trần Hoàng Duy
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								28 tuổi, huấn luyện viên thể hình
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Mình có chuyên môn nhưng không biết làm sao để đưa khóa học ra
							thị trường. Nhờ khóa Bán hàng qua khóa học, mình hiểu được cách
							định giá, xây dựng ưu đãi và dùng email marketing để chăm sóc
							học viên. Hiện tại, doanh thu từ khóa học online của mình gấp 3
							lần so với việc dạy offline.
						</p>
					</div>
				</div>
			</div>

			<!-- Testimonial 3: Lê Hồng Nhung -->
			<div
				class="ml-10 mt-10 mr-2 h-full w-[352px]"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
			>
				<div
					class="h-full bg-white rounded-[30px] relative hover:shadow-2xl transition-all duration-300 cursor-pointer group"
				>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-3-avatar.png') }}"
						alt="Lê Hồng Nhung"
						width="112"
						height="112"
						class="absolute -top-6 -left-10 size-28 rounded-full object-cover"
						style="box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.2)"
					/>
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/quote.svg') }}"
						alt=""
						srcset=""
						width="25"
						height="32"
						class="w-6 h-8 absolute top-0 right-4 -translate-y-1/2"
					/>
					<div class="!pt-6 !px-7 !pb-12">
						<!-- Profile Section -->
						<div class="!mb-4 ml-18">
							<h3
								class="text-lg leading-normal mb-2 font-semibold text-[#00238F]"
							>
								Lê Hồng Nhung
							</h3>
							<p
								class="text-sm leading-normal mb-2 font-normal text-[#5A5A5A]"
							>
								38 tuổi, trưởng phòng thiết kế
							</p>
							<!-- Star Rating -->
							<div class="flex">
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/star.svg') }}"
									alt=""
									width="16"
									height="16"
									class="inline-block w-4 h-4"
								/>
							</div>
						</div>

						<!-- Testimonial Text -->
						<p
							class="text-[#212121] text-base leading-normal font-normal text-justify"
						>
							Chúng tôi đã triển khai thành công sử dụng hình ảnh và video
							bằng AI với khóa học: Thiết kế Hình ảnh & Video 5s siêu thực với
							AI của KIMEDIA. Giờ các nhân sự đều biết làm ảnh và video nhanh
							chóng, hiệu quả để sử dụng cho các chiến dịch quảng cáo.
						</p>
					</div>
				</div>
			</div>
		</div>

		<!-- Pagination Dots -->
	</div>

	<div class="container">
		<div class="mt-14 grid grid-cols-1 md:grid-cols-2 grid-rows-2 gap-x-6 gap-y-5">
			<div
				class="youtube-video-container"
				data-aos-duration="250"
				data-x-aos="zoom-in"
				data-video-src="https://www.youtube.com/embed/pqDrLmK6Xsg?si=F0GlIbj7-xg7sPPi&amp;autoplay=1&amp;rel=0&amp;modestbranding"
				data-poster="{{ asset('assets/frontend/maikimquy/assets/images/youtube-thumbnail.png') }}"
			>
				<div class="video-container relative overflow-hidden">
					<div class="poster-wrapper">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-card-1.jpg') }}"
							alt="MGK Beauty Academy Video"
							class="w-full h-auto aspect-video md:aspect-auto md:w-[620px] md:h-[349px] object-cover"
						/>
						<!-- Play button overlay -->
						<div class="absolute inset-0 flex items-center justify-center">
							<div
								class="w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform cursor-pointer"
							>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/play.svg') }}"
									alt=""
									width="54"
									height="54"
									class="xl:size-full"
								/>
							</div>
						</div>
					</div>

					<!-- YouTube iframe container (initially empty) -->
					<div
						class="iframe-container hidden aspect-video md:aspect-auto md:w-[620px] md:h-[349px]"
					></div>
				</div>
			</div>
			<div
				class="youtube-video-container"
				data-aos-duration="250"
				data-x-aos="zoom-in"
				data-video-src="https://www.youtube.com/embed/pqDrLmK6Xsg?si=F0GlIbj7-xg7sPPi&amp;autoplay=1&amp;rel=0&amp;modestbranding"
				data-poster="assets/images/youtube-thumbnail.png"
			>
				<div class="video-container relative overflow-hidden">
					<div class="poster-wrapper">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-card-1.jpg') }}"
							alt="MGK Beauty Academy Video"
							class="w-full h-auto aspect-video md:aspect-auto md:w-[620px] md:h-[349px] object-cover"
						/>
						<!-- Play button overlay -->
						<div class="absolute inset-0 flex items-center justify-center">
							<div
								class="w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform cursor-pointer"
							>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/play.svg') }}"
									alt=""
									width="54"
									height="54"
									class="xl:size-full"
								/>
							</div>
						</div>
					</div>

					<!-- YouTube iframe container (initially empty) -->
					<div
						class="iframe-container hidden aspect-video md:aspect-auto md:w-[620px] md:h-[349px]"
					></div>
				</div>
			</div>
			<div
				class="youtube-video-container"
				data-aos-duration="250"
				data-x-aos="zoom-in"
				data-video-src="https://www.youtube.com/embed/pqDrLmK6Xsg?si=F0GlIbj7-xg7sPPi&amp;autoplay=1&amp;rel=0&amp;modestbranding"
				data-poster="assets/images/youtube-thumbnail.png"
			>
				<div class="video-container relative overflow-hidden">
					<div class="poster-wrapper">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-card-1.jpg') }}"
							alt="MGK Beauty Academy Video"
							class="w-full h-auto aspect-video md:aspect-auto md:w-[620px] md:h-[349px] object-cover"
						/>
						<!-- Play button overlay -->
						<div class="absolute inset-0 flex items-center justify-center">
							<div
								class="w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform cursor-pointer"
							>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/play.svg') }}"
									alt=""
									width="54"
									height="54"
									class="xl:size-full"
								/>
							</div>
						</div>
					</div>

					<!-- YouTube iframe container (initially empty) -->
					<div
						class="iframe-container hidden aspect-video md:aspect-auto md:w-[620px] md:h-[349px]"
					></div>
				</div>
			</div>
			<div
				class="youtube-video-container"
				data-aos-duration="250"
				data-x-aos="zoom-in"
				data-video-src="https://www.youtube.com/embed/pqDrLmK6Xsg?si=F0GlIbj7-xg7sPPi&amp;autoplay=1&amp;rel=0&amp;modestbranding"
				data-poster="assets/images/youtube-thumbnail.png"
			>
				<div class="video-container relative overflow-hidden">
					<div class="poster-wrapper">
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/feedback-card-1.jpg') }}"
							alt="MGK Beauty Academy Video"
							class="w-full h-auto aspect-video md:aspect-auto md:w-[620px] md:h-[349px] object-cover"
						/>
						<!-- Play button overlay -->
						<div class="absolute inset-0 flex items-center justify-center">
							<div
								class="w-14 h-14 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform cursor-pointer"
							>
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/play.svg') }}"
									alt=""
									width="54"
									height="54"
									class="xl:size-full"
								/>
							</div>
						</div>
					</div>

					<!-- YouTube iframe container (initially empty) -->
					<div
						class="iframe-container hidden aspect-video md:aspect-auto md:w-[620px] md:h-[349px]"
					></div>
				</div>
			</div>
		</div>
	</div>

	<div id="khoa-hoc" class="container pt-20">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-3 gap-y-14">
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="50" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg1.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Khoá học tài chính cá nhân<br>Chuyên gia: Hoàng Mai
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg1-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg1-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="100" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg2.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Khủng Hoảng hiện Sinh<br>Chuyên Gia: Thanh Vân
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg2-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg2-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="150" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg3.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Quản trị trường mầm non<br>Chuyên Gia: Thanh Nhàn
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg3-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg3-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="200" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg4.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Nghệ Thuật Thuyết Trình<br>Chuyên gia: Tô Hiền
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg4-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg4-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="250" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg5.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Phun xăm chân mày<br>Chuyên gia: Hải Nam
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg5-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg5-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="300" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg6.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Bán hàng ngành làm đẹp<br>Chuyên gia: Diệu Thu
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg6-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg6-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="350" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg7.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Trải nghiệm khách hàng Spa<br>Chuyên gia: Giang Lê
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg7-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg7-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="400" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg8.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Giảng dạy tiếng Anh<br>Chuyên gia: Bùi Huệ
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg8-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg8-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="450" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg9.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Nghệ thuật giảng dạy<br>Chuyên gia: Huỳnh Khôi
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg9-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg9-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="500" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg10.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Nghệ thuật kê đơn<br>Bác Sĩ: Quỳnh
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg10-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg10-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="550" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg11.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Tài Chính Cho Sếp<br>Chuyên gia: Minh Đỗ
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg11-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg11-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
			<div class="h-[210px] relative bg-white rounded-[14.40px] !p-2" data-aos-lazy="fade-up" data-aos-delay="600" data-x-aos="fade-up">
				<div class="flex items-center gap-2">
					<img class="w-[46.75px] h-[46.75px] rounded-full" src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg12.png">
					<div class="text-[#111111] text-sm font-extrabold">
						Cải thiện giọng nói<br>Chuyên Gia: Huyền Voice
					</div>
				</div>
				<!-- <img
					class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
					src="https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg12-detail.png"
				/> -->
				<div class="w-full h-[195px] rounded-lg" style="
					background: url('https://maikimquy.com/assets/frontend/mst-academy/assets/images/chuyengia/cg12-detail.png');
					background-repeat: no-repeat;
					background-position: left top;
					background-size: cover;
					background-attachment: scroll;
					background-origin: content-box;
					"></div>
			</div>
		</div>
	</div>

	<div class="container">
		<!-- Benefits Section -->
		<div
			class="mt-[120px] !py-[30px] !pr-7 !pl-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-x-2.5 gap-y-4 rounded-lg md:rounded-xl lg:rounded-2xl xl:rounded-[30px] bg-gradient-to-b from-[#1540FF] to-[#00238F]"
			data-x-aos="fade-up"
		>
			<div
				class="col-span-1 sm:col-span-2 lg:col-span-3 xl:col-span-2 text-white"
				data-x-aos="fade-right"
				data-aos-delay="200"
			>
				<div class="h-full !px-5 flex flex-col justify-center items-start">
					<div class="!mb-5">
						<div
							class="text-lg md:text-xl lg:text-3xl xl:text-[42px] xl:leading-tight font-extrabold flex items-center space-x-2"
						>
							<span> QUYỀN LỢI </span>
							<div class="!px-4 md:!px-6 xl:!px-9 !py-2 relative">
								<img
									src="{{ asset('assets/frontend/maikimquy/assets/images/hero-left-highlight-bg.svg') }}"
									alt=""
									width="162"
									height="44"
									class="w-full h-auto absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
								/>
								<span
									class="relative z-[1] inline-block text-base md:text-xl xl:text-2xl leading-[1.2] text-[#00238F] -rotate-3"
									>Đặc biệt</span
								>
							</div>
						</div>
						<p
							class="text-base md:text-lg lg:text-xl xl:text-2xl leading-[1.4] font-bold mt-2"
						>
							DÀNH RIÊNG CHO HỌC VIÊN KIMEDIA
						</p>
					</div>
					<p
						class="text-sm md:text-base lg:text-lg xl:leading-6 font-normal text-justify"
					>
						Khi tham gia các khóa học tại KIMEDIA, bạn không chỉ nhận được kiến
						thức chuyên sâu mà còn được đồng hành cùng đội ngũ chuyên gia, cộng
						đồng học viên năng động và hệ thống hỗ trợ tối ưu cho quá trình học
						tập online.
					</p>
				</div>
			</div>

			<!-- Benefit 1 -->
			<div
				class="bg-white rounded-[30px] !px-4 !py-3.5 hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="flex items-center gap-x-3 !mb-5">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/wifi-icon.svg') }}"
						alt=""
						width="58px"
						height="58px"
						class="w-[58px] h-[58px] transition-transform duration-300"
					/>
					<h4 class="text-[#00238F] font-bold text-[17px]">
						Học mọi lúc, mọi nơi, trên mọi thiết bị
					</h4>
				</div>
				<p class="text-[#212121] text-sm md:text-base leading-[1.4] text-justify">
					Chỉ cần một chiếc điện thoại hoặc máy tính kết nối internet, bạn có thể
					học vào bất kỳ thời gian nào phù hợp - không giới hạn thời lượng truy
					cập.
				</p>
			</div>

			<!-- Benefit 2 -->
			<div
				class="bg-white rounded-[30px] !px-4 !py-3.5 hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="flex items-center gap-x-3 !mb-5">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/chat.svg') }}"
						alt=""
						width="58px"
						height="58px"
						class="w-[58px] h-[58px] group-hover:scale-110 transition-transform duration-300"
					/>
					<h4 class="text-[#00238F] font-bold text-[17px]">
						Tư vấn 1-1 cùng giảng viên & chuyên gia
					</h4>
				</div>
				<p class="text-[#212121] text-sm md:text-base leading-[1.4] text-justify">
					Mỗi học viên được tư vấn cá nhân trong quá trình học, giúp giải quyết
					đúng vấn đề bạn đang gặp phải và cá nhân hóa lộ trình học tập.
				</p>
			</div>

			<!-- Benefit 3 -->
			<div
				class="bg-white rounded-[30px] !px-4 !py-3.5 hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="flex items-center gap-x-3 !mb-5">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/community.svg') }}"
						alt=""
						width="58px"
						height="58px"
						class="w-[58px] h-[58px] group-hover:scale-110 transition-transform duration-300"
					/>
					<h4 class="text-[#00238F] font-bold text-[17px]">
						Tham gia Group giải đáp cho học viên
					</h4>
				</div>
				<p class="text-[#212121] text-sm md:text-base leading-[1.4] text-justify">
					Được kết nối với hàng nghìn học viên trong cộng đồng nội bộ KIMEDIA để
					chia sẻ kinh nghiệm, học hỏi và cập nhật xu hướng đào tạo mới nhất.
				</p>
			</div>

			<!-- Benefit 4 -->
			<div
				class="bg-white rounded-[30px] !px-4 !py-3.5 hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="flex items-center gap-x-3 !mb-5">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/presentation.svg') }}"
						alt=""
						width="58px"
						height="58px"
						class="w-[58px] h-[58px] group-hover:scale-110 transition-transform duration-300"
					/>
					<h4 class="text-[#00238F] font-bold text-[17px]">
						Cập nhật bài giảng & tài liệu liên tục
					</h4>
				</div>
				<p class="text-[#212121] text-sm md:text-base leading-[1.4] text-justify">
					Kho học liệu, template, tài liệu hướng dẫn được cập nhật định kỳ, giúp
					bạn không bao giờ bị lạc hậu giữa thị trường số.
				</p>
			</div>

			<!-- Benefit 5 -->
			<div
				class="bg-white rounded-[30px] !px-4 !py-3.5 hover:scale-105 hover:shadow-xl transition-all duration-300 cursor-pointer group"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="flex items-center gap-x-3 !mb-5">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/certificate.svg') }}"
						alt=""
						width="58px"
						height="58px"
						class="w-[58px] h-[58px] group-hover:scale-110 transition-transform duration-300"
					/>
					<h4 class="text-[#00238F] font-bold text-[17px]">
						Nhận chứng nhận hoàn thành khóa học
					</h4>
				</div>
				<p class="text-[#212121] text-sm md:text-base leading-[1.4] text-justify">
					Mỗi khóa học đều có bài kiểm tra cuối kỳ và cấp chứng nhận online – bạn
					có thể dùng để xây dựng uy tín cá nhân hoặc hồ sơ chuyên môn.
				</p>
			</div>

			<!-- CTA Button -->
			<div class="text-white text-center" data-x-aos="fade-up" data-aos-delay="300">
				<div class="h-full flex flex-col justify-center items-center">
					<h3
						class="text-base md:text-lg lg:text-xl xl:text-2xl leading-[1.4] font-bold mb-1"
					>
						Bắt đầu học cùng<br />
					</h3>
					<p
						class="text-lg md:text-xl lg:text-3xl xl:text-[42px] leading-tight font-extrabold !mb-4 lg:!mb-8"
					>
						KIMEDIA
					</p>
					<a
						href="tel:0846843456"
						class="bg-[#FFB546] text-[17px] leading-7 !px-5 !py-2 w-fit rounded-full flex items-center gap-2 hover:bg-orange-500 text-[#00238F] font-bold transition-colors duration-300"
						style="box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.15)"
					>
						<span> LIÊN HỆ NGAY </span>
						<img
							src="{{ asset('assets/frontend/maikimquy/assets/images/arrow-right.svg') }}"
							alt=""
							width="24px"
							height="22px"
							class="w-6 h-auto"
						/>
					</a>
				</div>
			</div>
		</div>
	</div>
</section>

<section
	class="services !pt-[55px] !pb-[50px] bg-center bg-no-repeat bg-cover"
	style="background-image: url({{ asset('assets/frontend/maikimquy/assets/images/service-bg.svg') }}"
>
	<div class="container mx-auto !px-4">
		<!-- Services Section -->
		<div class="text-center">
			<h2
				class="text-lg md:text-xl lg:text-3xl xl:text-[42px]  !mb-5 md:!mb-8 xl:!mb-12 font-extrabold text-[#00238F]"
			>
				DỊCH VỤ KIMEDIA
			</h2>
		</div>

		<!-- Services Grid -->
		<div
			class="flex !flex-wrap lg:!flex-nowrap justify-center gap-4 md:gap-6 lg:gap-8 !mb-10"
		>
			<!-- Service 1 - E-Learning -->
			<div
				class="w-full md:w-2/5 lg:w-1/3 bg-white rounded-[30px] p-3 !pb-10 text-center overflow-hidden"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="mb-3">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/service-1.png') }}"
						alt="E-Learning System"
						class="w-full aspect-square rounded-[20px]"
					/>
				</div>
				<h3
					class="text-sm md:text-base xl:text-lg leading-6 font-bold uppercase text-[#00238F]"
				>
					HỆ THỐNG E-LEARNING
				</h3>
			</div>

			<!-- Service 2 - Expert Development -->
			<div
				class="w-full md:w-2/5 lg:w-1/3 bg-white rounded-[30px] p-3 !pb-10 text-center overflow-hidden"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="mb-3">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/service-2.png') }}"
						alt="Expert Development"
						class="w-full aspect-square rounded-[20px]"
					/>
				</div>
				<h3 class="text-lg leading-6 font-bold uppercase text-[#00238F]">
					Xây dựng THCN chuyên gia
				</h3>
			</div>

			<!-- Service 3 - Business Development -->
			<div
				class="w-full md:w-2/5 lg:w-1/3 bg-white rounded-[30px] p-3 !pb-10 text-center overflow-hidden"
				style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2)"
				data-x-aos="fade-up"
				data-aos-delay="300"
			>
				<div class="mb-3">
					<img
						src="{{ asset('assets/frontend/maikimquy/assets/images/service-3.png') }}"
						alt="Business Development"
						class="w-full aspect-square rounded-[20px]"
					/>
				</div>
				<h3 class="text-lg leading-6 font-bold uppercase text-[#00238F]">
					Đồng hành kinh doanh tri thức
				</h3>
			</div>
		</div>

		<!-- CTA Button -->
		<div class="flex justify-center" data-x-aos="fade-right" data-aos-delay="300">
			<a
				href="tel:0846843456"
				class="animate-pulse-shadow bg-[#FFB546] text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl leading-[1.4] !pl-10 !py-2.5 !pr-7 w-fit rounded-full flex items-center gap-2 hover:bg-orange-500 text-[#00238F] font-bold transition-colors duration-300"
			>
				<span> Liên hệ KIMEDIA </span>
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/arrow-right.svg') }}"
					alt=""
					width="24px"
					height="22px"
					class="w-6 h-auto"
				/>
			</a>
		</div>
	</div>
</section>

<section
	class="faq !py-10 md:!py-20 bg-no-repeat bg-center bg-cover"
	style="background-image: url({{ asset('assets/frontend/maikimquy/assets/images/faq-bg.png') }}"
>
	<div class="container">
		<div class="flex gap-8 lg:gap-12 flex-col lg:flex-row items-start">
			<!-- Left Column - FAQ Khóa Học -->
			<div class="lg:w-[308px] w-full shrink-0">
				<div>
					<!-- FAQ Header -->
					<div class="text-center">
						<div
							style="background-image: url({{ asset('assets/frontend/maikimquy/assets/images/faq-chat.svg') }}"
							class="mx-auto lg:mx-0 w-64 xl:w-[280px] xl:h-[70px] text-base md:text-xl xl:text-[26.32px] !pt-2 !pb-4 xl:!pt-2.5 xl:!pb-5 flex justify-center items-center text-[#00238F] tracking-[-0.263px] font-extrabold bg-no-repeat bg-contain bg-center"
						>
							FAQ Khóa Học
						</div>

						<h2
							class="text-xl md:text-4xl xl:text-[42px] xl:leading-tight mt-1 font-extrabold text-white mb-3.5"
						>
							CÂU HỎI <br />
							THƯỜNG GẶP
						</h2>
						<p
							class="text-white text-sm md:text-base xl:text-lg font-normal text-center"
						>
							Mỗi hành trình học tập bắt đầu từ một dấu hỏi. Hãy khám phá
							những thắc mắc phổ biến nhất để tự tin bắt đầu và chinh phục mục
							tiêu của bạn!
						</p>
					</div>
				</div>
			</div>

			<!-- Right Column - FAQ Items -->
			<div class="space-y-4 w-full text-sm md:text-base">
				<!-- FAQ Item 1 -->
				<div
					data-x-aos="fade-up"
					class="faq-item bg-white group rounded-xl shadow-lg overflow-hidden"
				>
					<button
						class="faq-question group-hover:text-[#1540FF] bg-white text-lg xl:leading-5 w-full !px-6 !py-4 border-0 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
						onclick="toggleFAQ(1)"
					>
						<span
							>1. Tôi không có kỹ năng công nghệ, liệu có học được
							không?</span
						>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
							class="faq-icon"
						>
							<path
								d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
								fill="currentColor"
							/>
						</svg>
					</button>
					<div class="faq-answer !px-6 !pb-4 hidden">
						<p class="text-[#212121] text-base leading-relaxed">
							Hoàn toàn có thể! Các khóa học tại KIMEDIA được thiết kế đơn
							giản, từng bước một. Bạn sẽ được hướng dẫn từ A-Z, kể cả khi bạn
							chưa từng quay video, chưa từng làm web hay biết chạy quảng cáo.
						</p>
					</div>
				</div>

				<!-- FAQ Item 2 -->
				<div
					data-x-aos="fade-up"
					data-aos-delay="200"
					class="faq-item bg-white group rounded-xl shadow-lg overflow-hidden"
				>
					<button
						class="faq-question group-hover:text-[#1540FF] bg-white text-lg xl:leading-5 w-full !px-6 !py-4 border-0 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
						onclick="toggleFAQ(2)"
					>
						<span
							>2. Tôi chưa có thương hiệu cá nhân thì học có hiệu quả
							không?</span
						>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
							class="faq-icon"
						>
							<path
								d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
								fill="currentColor"
							/>
						</svg>
					</button>
					<div class="faq-answer !px-6 !pb-4 hidden">
						<p class="text-[#212121] text-base leading-relaxed">
							Thời gian học tùy thuộc vào khóa học bạn chọn. Khóa cơ bản
							thường kéo dài 2-3 tháng, khóa nâng cao có thể từ 4-6 tháng.
							Chúng tôi có lịch học linh hoạt phù hợp với thời gian của bạn.
						</p>
					</div>
				</div>

				<!-- FAQ Item 3 -->
				<div
					data-x-aos="fade-up"
					data-aos-delay="400"
					class="faq-item bg-white group rounded-xl shadow-lg overflow-hidden"
				>
					<button
						class="faq-question group-hover:text-[#1540FF] bg-white text-lg xl:leading-5 w-full !px-6 !py-5 border-0 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
						onclick="toggleFAQ(3)"
					>
						<span>3. Học online thì ai hỗ trợ tôi trong quá trình học?</span>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
							class="faq-icon"
						>
							<path
								d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
								fill="currentColor"
							/>
						</svg>
					</button>
					<div class="faq-answer !px-6 !pb-4 hidden">
						<p class="text-[#212121] text-base leading-relaxed">
							Khóa học được tổ chức trực tiếp tại trung tâm với đầy đủ trang
							thiết bị chuyên nghiệp. Chúng tôi cũng có hỗ trợ học online cho
							những buổi lý thuyết để bạn có thể học mọi lúc mọi nơi.
						</p>
					</div>
				</div>

				<!-- FAQ Item 4 -->
				<div
					data-x-aos="fade-up"
					data-aos-delay="600"
					class="faq-item bg-white group rounded-xl shadow-lg overflow-hidden"
				>
					<button
						class="faq-question group-hover:text-[#1540FF] bg-white text-lg xl:leading-5 w-full !px-6 !py-4 border-0 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
						onclick="toggleFAQ(4)"
					>
						<span>4. Tôi nên bắt đầu từ khóa nào nếu chưa biết gì?</span>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="25"
							height="25"
							viewBox="0 0 25 25"
							fill="none"
							class="faq-icon"
						>
							<path
								d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
								fill="currentColor"
							/>
						</svg>
					</button>
					<div class="faq-answer !px-6 !pb-4 hidden">
						<p class="text-[#212121] text-base leading-relaxed">
							Có! Sau khi hoàn thành khóa học và vượt qua bài kiểm tra cuối
							khóa, bạn sẽ nhận được chứng chỉ hoàn thành khóa học do trung
							tâm cấp. Chứng chỉ này sẽ giúp bạn tự tin hơn khi xin việc hoặc
							mở spa riêng.
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>

<section id="tin-tuc" class="news bg-white !py-10 md:!py-16 xl:!pt-[55px] xl:!pb-[50px]">
	<div class="container mx-auto !px-4">
		<!-- News Section -->
		<div class="text-center mb-8 md:mb-10 xl:mb-14">
			<h2
				class="text-lg md:text-xl lg:text-4xl xl:text-[42px] font-extrabold gradient-text !leading-[inherit]"
			>
				TIN TỨC & BLOG TỪ KIMEDIA
			</h2>
		</div>

		<!-- News Grid -->
		<div
			class="slider-container gap-8 mb-12"
			data-x-aos="fade-up"
			data-aos-duration="200"
		>
			@if(isset($blogs) && count($blogs) > 0)
				@foreach($blogs as $key => $blog)
					<div class="!px-3 !py-4">
						<div
							class="!px-3 !pt-2.5 rounded-[30px] bg-white overflow-hidden transition-shadow duration-300"
							style="box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2)"
						>
							<div class="aspect-[3/2] overflow-hidden">
								<img
									src="{{ get_image($blog->thumbnail) }}"
									alt="{{ $blog->title }}"
									class="w-full h-full object-cover rounded-[20px] hover:scale-105 transition-transform duration-300"
								/>
							</div>
							<div class="!py-6">
								<h3
									class="text-lg text-center font-bold text-[#00238F] mb-4 line-clamp-3"
								>
									{{ $blog->title }}
								</h3>
								<a
									href="{{ route('blog.details', $blog->slug) }}"
									class="!py-1.5 !pl-7 !pr-5 bg-[#e5eaff] rounded-full border border-solid border-white text-[#1540FF] text-base leading-normal font-semibold mx-auto w-fit flex items-center gap-1 mt-4"
									style="
										box-shadow: 0px 0px 5.48px 0px rgba(96, 116, 218, 0.46)
											inset;
									"
								>
									<span>Xem chi tiết</span>
									<img
										src="{{ asset('assets/frontend/maikimquy/assets/images/caret-right.svg') }}"
										alt=""
										width="15"
										height="15"
										class="w-4 h-4"
									/>
								</a>
							</div>
						</div>
					</div>
				@endforeach
			@endif
		</div>
	</div>
</section>

<footer id="footer" class="footer !pt-10 !pb-14 bg-gradient-to-b from-[#E2F1FF] to-[#EAF5FF]">
	<div class="container mx-auto !px-4">
		<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
			<!-- Logo and Contact Info -->
			<div class="col-span-1 md:col-span-3 lg:col-span-1">
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/logo-footer.png') }}"
					alt="KIMEDIA Logo"
					width="228"
					height="49"
					class="mb-2 h-12 w-auto"
				/>
				<p class="ml-1 text-lg leading-6 font-bold text-[#1540FF]">
					CÔNG TY TNHH KIMEDIA
				</p>
			</div>

			<div class="flex items-center gap-2.5">
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/telephone.svg') }}"
					alt=""
					width="58px"
					height="58px"
					class="size-6 md:size-10 lg:size-[58px]"
				/>
				<div class="text-[17px] font-bold">
					<p class="text-black">Hotline:</p>
					<a href="tel:0846843456" class="text-[#1540FF]">************</a>
				</div>
			</div>

			<div class="flex items-center gap-2.5">
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/map-maker.svg') }}"
					alt=""
					width="58px"
					height="58px"
					class="size-6 md:size-10 lg:size-[58px]"
				/>
				<div class="text-[17px] font-bold">
					<p class="text-black">Address</p>
					<a href="https://maps.app.goo.gl/dpTxZDH2Cn61a8A36" class="text-[#1540FF]">33 ngõ 41 Thái Hà, Hà Nội</a>
				</div>
			</div>

			<div class="flex items-center gap-2.5">
				<img
					src="{{ asset('assets/frontend/maikimquy/assets/images/envelop.svg') }}"
					alt=""
					width="58px"
					height="58px"
					class="size-6 md:size-10 lg:size-[58px]"
				/>
				<div class="text-[17px] font-bold">
					<p class="text-black">Email:</p>
					<a href="mailto:<EMAIL>" class="text-[#1540FF]"><EMAIL></a>
				</div>
			</div>
		</div>

		<hr class="mt-[67px] mb-[30px]" style="background: rgba(0, 35, 143, 0.09)" />

		<!-- Bottom Border -->
		<div class="text-base font-normal text-[#333]">
			<div
				class="flex flex-col-reverse md:flex-row justify-between items-center gap-4"
			>
				<p class="">© 2025 KIMEDIA. All Rights Reserved.</p>
				<div class="flex flex-col md:flex-row items-center gap-3">
					<p class="block">Kết nối thêm với KIMEDIA qua:</p>
					<div class="flex gap-2">
						<a class="block" href="#">
							<img src="{{ asset('assets/frontend/maikimquy/assets/images/Facebook.svg') }}" alt="" width="29" />
						</a>
						<a class="block" href="#">
							<img src="{{ asset('assets/frontend/maikimquy/assets/images/google.svg') }}" alt="" width="29" />
						</a>
						<a class="block" href="#">
							<img src="{{ asset('assets/frontend/maikimquy/assets/images/Youtube.svg') }}" alt="" width="29" />
						</a>
						<a class="block" href="#">
							<img src="{{ asset('assets/frontend/maikimquy/assets/images/Instagram.svg') }}" alt="" width="29" />
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>

<!-- toster file -->
<div class="toast-container position-fixed top-0 end-0 p-3"></div>
@include('partials.modals.register_login')
@endsection
